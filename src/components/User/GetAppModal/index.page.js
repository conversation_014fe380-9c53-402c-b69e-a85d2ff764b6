import { Modal } from "antd";
import React from "react";
import {  Io<PERSON>ogo<PERSON><PERSON>le, IoLogoWindows, IoLogoAndroid } from "react-icons/io";
import { FaAppStoreIos } from "react-icons/fa";
import { ReactComponent as Logo } from "./Assets/daakia.svg";
import "./GetAppModal.scss";
import { triggerDownloadByOS } from "../../../utils";
import { OS_PLATFORM } from "../../../utils/constants";

export default function GetAppModal({
  title,
  show,
  onHide,
  android = true,
  ios = true,
  windows = true,
  mac = true,
}) {
  return (
    <Modal
      title={
        <div className="getAppModal-title">
          <Logo />
          <span>{title}</span>
        </div>
      }
      open={show}
      onCancel={onHide}
      className="getAppModal"
      footer={null}
    >
      <div className="text-center">
        {/* <h2 className="font-eb">Download The Daakia App</h2> */}
        <p className="getAppModal-sub">
          Download the Daakia App and Enjoy a Seamless Communication Experience
          With Your Friends, Family and Colleagues.
        </p>
        <div className="d-flex justify-content-center gap-3 download-buttons">
          {android && (
            <a
              href="https://play.google.com/store/apps/details?id=com.app.daakia"
              target="_blank"
              rel="noreferrer"
              className="d-flex align-items-center gap-1"
            >
              <IoLogoAndroid
                style={{
                  fontSize: "25px",
                  color: "#81BB00",
                }}
              />
              <span>Android</span>
            </a>
          )}
          {ios && (
            <a
              href="https://apps.apple.com/in/app/daakia-chat-call-conference/id1577764111"
              target="_blank"
              rel="noreferrer"
              className="d-flex align-items-center gap-1"
            >
              <FaAppStoreIos
                style={{
                  fontSize: "25px",
                  color: "#1AA0F7",
                }}
              />
              <span>Apple</span>
            </a>
          )}
        </div>
        <div className="d-flex justify-content-center mt-3 gap-3 download-buttons">
          {windows && (
            <a
              rel="noreferrer"
              target="_blank"
              className="d-flex align-items-center gap-1"
              onClick={(e) => {
                triggerDownloadByOS(OS_PLATFORM.WINDOWS);
                onHide();
                e.preventDefault();
              }}
            >
              <IoLogoWindows
                style={{
                  fontSize: "25px",
                  color: "#0077d4",
                }}
              />
              <span>Windows</span>
            </a>
          )}
          {mac && (
            <a
              rel="noreferrer"
              target="_blank"
              className="d-flex align-items-center gap-1"
              onClick={(e) => {
                triggerDownloadByOS(OS_PLATFORM.MAC);
                onHide();
                e.preventDefault();
              }}
            >
              <IoLogoApple
                style={{
                  fontSize: "30px",
                  color: "#fff",
                }}
              />
              <span>Mac OS</span>
            </a>
          )}
        </div>
      </div>
    </Modal>
  );
}
