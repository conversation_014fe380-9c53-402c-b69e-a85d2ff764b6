.download-desktop-app{
    background-color: #13294B;
    margin-left: 1rem;
    width: 10.5rem;
    border-radius: 5px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    cursor: pointer;
    &-inner{
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    &-icon{
        width: 20px;
    }
    &-text{
        display: flex;
        color: white;
        flex-direction: column;
        p{
            font-size: 9px !important;
            margin: 0 !important;
            line-height: 16px !important;
        }
        span{
            font-size: 13px !important;
            font-weight: bold !important;
        }
    }
    @media screen and (max-width: 1201px){
        &-text{
            p{
                font-size: 8px !important;
                line-height: 14px !important;
            }
            span{
                font-size: 10px !important;
            }
        }
    }
    @media screen and (max-width: 993px){
        &-text{
            p{
                font-size: 10px !important;
                line-height: 18px !important;
                display: flex;
            }
            span{
                font-size: 13px !important;
            }
        }
    }
    @media screen and (max-width: 550px){
        &-text{
            p{
                font-size: 8.5px !important;
            }
            span{
                font-size: 11.5px !important;
            }
        }
    }
    @media screen and (max-width: 509px){
        &-text{
            p{
                font-size: 8px !important;
                line-height: 16px !important;
            }
            span{
                font-size: 10px !important;
            }
        }
    }
    @media screen and (max-width: 475px){
        &-text{
            p{
                font-size: 7px !important;
                line-height: 14px !important;
            }
            span{
                font-size: 9px !important;
            }
        }
    }
    @media screen and (max-width: 451px){
        &-text{
            p{
                font-size: 7px !important;
                line-height: 12px !important;
            }
            span{
                font-size: 8px !important;
                display: flex;
            }
        }
    }
    @media screen and (max-width: 427px){
        width: 176px;
        &-text{
            p{
                font-size: 6px !important;
                line-height: 10px !important;
            }
            span{
                font-size: 8px !important;
                display: flex;
            }
        }
    }
    @media screen and (max-width: 413px){
        width: 176px;
        &-text{
            p{
                font-size: 5.5px !important;
                line-height: 9.5px !important;
            }
            span{
                font-size: 7.5px !important;
                display: flex;
            }
        }
    }
    @media screen and (max-width: 402px){
        width: 180px;
        &-text{
            p{
                font-size: 5px !important;
                line-height: 9.5px !important;
            }
            span{
                font-size: 7px !important;
                display: flex;
            }
        }
    }
}