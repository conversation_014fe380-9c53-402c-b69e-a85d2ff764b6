.solution-cards{
    position: relative; 
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    width: 450px;
    height: auto;
    transition: 0.3s;
    overflow: hidden; 
    gap: 10px;
    margin: 10px;
    padding-bottom: 10px;

    @media (max-width: 768px){
        width: 100%;
    }
    img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 15px;
        transition: 0.3s;
        padding: 10px 10px 0;
      }

    div{
        // position: absolute;
        // top:20px;
        // left: 20px;
        // color: white;
        //  flex-direction: column;
        //  text-align: left;
        //  z-index: 1;
        padding: 10px 20px 0px;
        text-align: left;
        color: black;
        h3{
            font-size: 1.2rem;
            color: black;
            font-weight: 700;
            margin-bottom: 10px;

            @media (max-width: 576px){
                font-size: 1rem;
            }
        }
        p{
            color: black;
        }
    }
}
