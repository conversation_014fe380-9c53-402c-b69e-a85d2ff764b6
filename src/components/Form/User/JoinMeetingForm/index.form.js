import { Form, Formik } from "formik";
import React from "react";
import { t } from "i18next";
import { Input as TextInput } from "../../../Antd";
import { CommonButton, RippleEffect } from "../../../UiElement";

export default function JoinMeetingForm({
  onSubmit,
  handleJoinMeetingModalClose,
  joinMeetingloading,
}) {
  const initialValues = {
    meeting_url: "",
  };
  return (
    <Formik
      initialValues={{ ...initialValues }}
      onSubmit={onSubmit}
      enableReinitialize
    >
      {(props) => {
        return (
          <Form>
            <div className="modalHeader">
              <h3>{t("text.videoConferencing.startMeeting")}</h3>
            </div>
            <div className="modalForm">
              <div className="form-group">
                <label className="form-label font-bd">
                  {" "}
                  {t("text.videoConferencing.joinMeetingUrl")}
                </label>
                <div className="form-control-wrap">
                  <TextInput
                    className="form-control"
                    name="meeting_url"
                    type="text"
                    placeholder={t("text.videoConferencing.joinMeetingPlaceholder")}
                    setFieldValue={props.handleChange}
                  />
                </div>
              </div>
              <div className="text-end modalFooter">
                <RippleEffect extraClassName="me-2 me-sm-3" type="light">
                  <CommonButton
                    onClick={() => handleJoinMeetingModalClose()}
                    variant="info"
                  >
                    {t("text.common.cancel")}
                  </CommonButton>
                </RippleEffect>
                <RippleEffect>
                  <CommonButton
                    variant="primary"
                    type="submit"
                    loading={joinMeetingloading}
                  >
                    {t("text.videoConferencing.start")}
                  </CommonButton>
                </RippleEffect>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
}
