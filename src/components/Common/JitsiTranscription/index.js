import React, {useEffect, useRef} from "react";
import {Col, Row, List, Avatar} from "antd";
import {Card} from "react-bootstrap";


function JitsiTranscription({daakiaTranscription, visible}) {
    let listRef = useRef(null);

    useEffect(() => {
        if (listRef.current) {
            listRef.current.scrollTop = listRef.current.scrollHeight; // Or use another approach if the list has a fixed height
        }
    }, [daakiaTranscription]);

    const generateAvatar = (name) => {
        const first = name[0].toUpperCase() || "";
        const words = name?.split(" ");
        const last = words[1]?.[0].toUpperCase() || "";
        return(`${first}${last}`);
    };


    return(
        <div  className={`${visible ? "/*d-flex justify-content-end*/" : "d-none"}`} style={{"padding":"2px", width: "18%", backgroundColor: "#141414"}}>
            <Row>
                <Col ref={listRef} span={22} style={{height: "99vh"}} className={"overflow-y-auto"}>
            <div  className={`chat-message current-user  justify-content-start`}>
                 <List
                itemLayout="horizontal"
                dataSource={daakiaTranscription}
                renderItem={(item, index) => (
                    <List.Item id={index} style={{borderBottom: "0px"}}>
                        <Card className={"w-100 p-1"} style={{backgroundColor:"#141414", borderRadius: "4px 10px 10px 10px"}}>
                        <List.Item.Meta
                            avatar={<Avatar style={{backgroundColor: "#fcf8d7", color:"#820014", alignItems: "center"}}>{generateAvatar(item?.participantName)}</Avatar>}
                            title={
                                <div className={"d-flex align-items-center"} style={{justifyContent: "space-between"}}>
                                    <span className={"fw-bolder"} style={{color: "#8c8c8c", fontSize: "small"}}
                                          href="">{item?.participantName}</span>
                                    <span style={{color: "#8c8c8c", fontSize: "small", marginRight: "20%"}}>{item?.time}</span>
                                </div>
                            }
                            description={<span className={"fw-bolder"} style={{color: "#FFF"}}> {item?.transcriptionChunk} </span>}
                            />
                        </Card>
                    </List.Item>
                )}
            />



                 {/* <Avatar src={message.avatarUrl} size="small"/> */}
                { /* {
                    daakiaTranscription.map((transcription) => (
                        <div className="message-content">
                            <p className={"ql-color-white"} style={{color: "#FFF"}}>{transcription.transcriptionChunk}</p>
                            {message.timestamp && (
                                <span style={{color: "#FFF"}} className="message-timestamp ql-color-white">{message.timestamp}</span>
                            )}
                        {<Dividere plain dashed/>}
                        </div>
                    ))
                } */ }

            </div>
                </Col>
            </Row>
        </div>
    )
}


export default JitsiTranscription;
