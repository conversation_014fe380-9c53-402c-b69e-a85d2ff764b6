import React from 'react';

function ErrorCard({ type, message }) {
  const getErrorIcon = (errorType) => {
    switch (errorType) {
      case 'key_insights':
        return '💡';
      case 'action_items':
        return '📋';
      case 'summary':
        return '📝';
      case 'participants':
        return '👥';
      case 'transcription':
        return '🎥';
      default:
        return '⚠️';
    }
  };

  const getErrorMessage = (errorType) => {
    switch (errorType) {
      case 'key_insights':
        return {
          title: 'Key Insights Unavailable',
          description: 'We\'re having trouble loading the key insights at the moment.',
          action: 'Please try refreshing the page or contact support if the issue persists.'
        };
      case 'action_items':
        return {
          title: 'Action Items Unavailable',
          description: 'We\'re having trouble loading the action items at the moment.',
          action: 'Please try refreshing the page or contact support if the issue persists.'
        };
      case 'summary':
        return {
          title: 'Summary Unavailable',
          description: 'We\'re having trouble loading the summary at the moment.',
          action: 'Please try refreshing the page or contact support if the issue persists.'
        };
      case 'participants':
        return {
          title: 'Participants Unavailable',
          description: 'We\'re having trouble loading the participants information.',
          action: 'You can continue viewing other sections while we resolve this.'
        };
      case 'transcription':
        return {
          title: 'Transcription Unavailable',
          description: 'We\'re having trouble loading the transcription.',
          action: 'Please try refreshing the page or contact support if the issue persists.'
        };
      default:
        return {
          title: 'Something Went Wrong',
          description: 'We encountered an unexpected issue.',
          action: 'Please try refreshing the page.'
        };
    }
  };

  const defaultError = getErrorMessage(type);
  const icon = getErrorIcon(type);

  // Use provided message if available, otherwise use default message
  const errorContent = message ? {
    title: defaultError.title,
    description: message,
    action: defaultError.action
  } : defaultError;

  return (
    <div className="error-container" style={{
      padding: '20px',
      margin: '10px 0',
      borderRadius: '8px',
      backgroundColor: '#fff3f3',
      border: '1px solid #ffcdd2',
      color: '#d32f2f'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'flex-start',
        gap: '12px'
      }}>
        <div style={{
          fontSize: '24px',
          lineHeight: '1'
        }}>
          {icon}
        </div>
        <div style={{
          flex: 1
        }}>
          <h4 style={{
            margin: '0 0 8px 0',
            fontSize: '16px',
            fontWeight: '600'
          }}>
            {errorContent.title}
          </h4>
          <p style={{
            margin: '0 0 8px 0',
            fontSize: '14px',
            color: '#666'
          }}>
            {errorContent.description}
          </p>
          <p style={{
            margin: '0',
            fontSize: '14px',
            color: '#666',
            fontStyle: 'italic'
          }}>
            {errorContent.action}
          </p>
        </div>
      </div>
    </div>
  );
}

export default ErrorCard; 