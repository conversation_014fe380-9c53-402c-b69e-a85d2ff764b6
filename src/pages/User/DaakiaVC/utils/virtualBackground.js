import { Track } from "livekit-client";
import { BackgroundBlur, VirtualBackground } from "@livekit/track-processors";

export async function toggleBlur(currentRoom, blur) {
  if (!currentRoom) return;

  try {
    const camTrackPublication =
      currentRoom.localParticipant.getTrackPublication(Track.Source.Camera);
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");
    if (
      camTrack.getProcessor() &&
      camTrack.getProcessor().name !== "background-blur"
    ) {
      await camTrack.stopProcessor();
    }
    await camTrack.setProcessor(BackgroundBlur(blur, { delegate: "GPU" }));
  } catch (e) {
    console.log("MyError1:", e);
    console.log(`ERROR: ${e.message}`);
  }
}

export async function toggleVirtualBackground(
  currentRoom,
  background
) {
  if (!currentRoom) return;

  try {
    const camTrackPublication =
      currentRoom.localParticipant.getTrackPublication(Track.Source.Camera);
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    // Check if we're already using the same background
    const currentProcessor = camTrack.getProcessor();
    if (currentProcessor && currentProcessor.name === "virtual-background") {
      // If we're already using a virtual background, check if it's the same one
      const currentBackground = currentProcessor.options?.background;
      if (currentBackground === background) {
        return; // No need to change if it's the same background
      }
    }

    // Create the new processor before stopping the old one
    const newProcessor = VirtualBackground(background);
    
    // Only stop the current processor if it exists and is different
    if (currentProcessor) {
      await camTrack.stopProcessor();
    }
    
    // Apply the new processor
    await camTrack.setProcessor(newProcessor);
  } catch (e) {
    console.log("MyError2:", e);
    console.log(`ERROR: ${e.message}`);
  }
}

export async function noEffect(room) {
  if (!room) return;
  try {
    const camTrackPublication = room.localParticipant.getTrackPublication(
      Track.Source.Camera
    );
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    if (camTrack.getProcessor()) {
      await camTrack.stopProcessor();
    }
  } catch (e) {
    console.log("MyError3:", e);
    console.log(`ERROR: ${e.message}`);
  }
}
