import {
  createLocalAudioTrack,
  createLocalTracks,
  createLocalVideoTrack,
  facingModeFromLocalTrack,
  Track,
  VideoPresets,
  Mutex,
} from "livekit-client";
import * as React from "react";
import { Modal } from "antd";
import { MdExpandMore } from "react-icons/md";
import {
  // MediaDeviceMenu,
  // TrackToggle,
  useMediaDevices,
  usePersistentUserChoices,
} from "@livekit/components-react";
import { onDeviceError } from "../utils/helper";
import { TrackToggle } from "../components/TrackToggle";
import "../styles/Prejoin.scss";
// import AudioVideoSettingsModal from "../components/AudioVideoSettingsModal/AudioVideoSettingsModal";
// import VirtualBackgroundModal from "../components/AudioVideoSettingsModal/VirtualBackgroundModal";
import AudioSettings from "../components/AudioVideoSettingsModal/AudioSettings";
import VideoSettings from "../components/AudioVideoSettingsModal/VideoSettings";

/**
 * @public
 */
export function usePreviewTracks(options, onError, setToastMessage, setToastStatus, setShowToast) {
  const [tracks, setTracks] = React.useState();
  const trackLock = React.useMemo(() => new Mutex(), []);

  React.useEffect(() => {
    let needsCleanup = false;
    let localTracks = [];
    trackLock.lock().then(async (unlock) => {
      try {
        if (options.audio || options.video) {
          localTracks = await createLocalTracks(options);

          if (needsCleanup) {
            localTracks.forEach((tr) => tr.stop());
          } else {
            setTracks(localTracks);
          }
        }
      } catch (e) {
        if (onError && e instanceof Error) {
          onError(e);
        } else {
          setToastMessage("Permissions denied for accessing audio/video devices.");
          setToastStatus("error");
          setShowToast(true);
          console.log(e);
        }
      } finally {
        unlock();
      }
    });

    return () => {
      needsCleanup = true;
      localTracks.forEach((track) => {
        track.stop();
      });
    };
  }, [JSON.stringify(options), onError, trackLock]);

  return tracks;
}

/** @public */
export function usePreviewDevice(enabled, deviceId, deviceKind) {
  const [deviceError, setDeviceError] = React.useState(null);
  const [isCreatingTrack, setIsCreatingTrack] = React.useState(false);

  const devices = useMediaDevices({ kind: deviceKind });
  const [selectedDevice, setSelectedDevice] = React.useState(undefined);

  const [localTrack, setLocalTrack] = React.useState();
  const [localDeviceId, setLocalDeviceId] = React.useState(deviceId);

  React.useEffect(() => {
    setLocalDeviceId(deviceId);
  }, [deviceId]);

  const prevDeviceId = React.useRef(localDeviceId); // Moved prevDeviceId declaration here

  const createTrack = async (id, kind) => {
    try {
      const track =
        kind === "videoinput"
          ? await createLocalVideoTrack({
              deviceId: id,
              resolution: VideoPresets.h720.resolution,
            })
          : await createLocalAudioTrack({ deviceId: id });

      const newDeviceId = await track.getDeviceId();
      if (newDeviceId && id !== newDeviceId) {
        prevDeviceId.current = newDeviceId;
        setLocalDeviceId(newDeviceId);
      }
      setLocalTrack(track);
    } catch (e) {
      if (e instanceof Error) {
        setDeviceError(e);
      }
    }
  };

  const switchDevice = async (track, id) => {
    await track.setDeviceId(id);
    prevDeviceId.current = id;
  };

  React.useEffect(() => {
    if (enabled && !localTrack && !deviceError && !isCreatingTrack) {
      setIsCreatingTrack(true);
      createTrack(localDeviceId, deviceKind).finally(() => {
        setIsCreatingTrack(false);
      });
    }
  }, [
    enabled,
    localTrack,
    deviceError,
    isCreatingTrack,
    localDeviceId,
    deviceKind,
  ]);

  React.useEffect(() => {
    if (!localTrack) {
      return;
    }
    if (!enabled) {
      localTrack.mute().then(() => console.log(localTrack.mediaStreamTrack));
    } else if (
      selectedDevice?.deviceId &&
      prevDeviceId.current !== selectedDevice?.deviceId
    ) {
      switchDevice(localTrack, selectedDevice.deviceId);
    } else {
      localTrack.unmute();
    }
  }, [localTrack, selectedDevice, enabled, deviceKind]);

  React.useEffect(() => {
    return () => {
      if (localTrack) {
        localTrack.stop();
        localTrack.mute();
      }
    };
  }, [localTrack]);

  React.useEffect(() => {
    setSelectedDevice(devices?.find((dev) => dev.deviceId === localDeviceId));
  }, [localDeviceId, devices]);

  return {
    selectedDevice,
    localTrack,
    deviceError,
  };
}

export function PreJoinAudioVideo({
  defaults = {},
  onValidate,
  onError,
  username,
  setIsValid,
  setUserChoices,
  persistUserChoices = true,
  setToastMessage,
  setToastStatus,
  setShowToast,
  setToastNotification,
  room,
  backgrounds,
  setBackgrounds,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  setDeviceIdAudio,
  ...htmlProps
}) {
  const [isAudioModalOpen, setIsAudioModalOpen] = React.useState(false);
  const [isVideoModalOpen, setIsVideoModalOpen] = React.useState(false);
  // const [isVisualEffectsModalOpen, setIsVisualEffectsModalOpen] = React.useState(false);

  const partialDefaults = {
    ...(defaults.audioDeviceId !== undefined && {
      audioDeviceId: defaults.audioDeviceId,
    }),
    ...(defaults.videoDeviceId !== undefined && {
      videoDeviceId: defaults.videoDeviceId,
    }),
    ...(defaults.audioEnabled !== undefined && {
      audioEnabled: defaults.audioEnabled,
    }),
    ...(defaults.videoEnabled !== undefined && {
      videoEnabled: defaults.videoEnabled,
    }),
    ...(defaults.username !== undefined && { username: defaults.username }),
  };

  const {
    userChoices: initialUserChoices,
    saveAudioInputDeviceId,
    saveAudioInputEnabled,
    saveVideoInputDeviceId,
    saveVideoInputEnabled,
    saveUsername,
  } = usePersistentUserChoices({
    defaults: partialDefaults,
    preventSave: !persistUserChoices,
    preventLoad: !persistUserChoices,
  });

  const [audioEnabled, setAudioEnabled] = React.useState(
    initialUserChoices.audioEnabled
  );
  const [videoEnabled, setVideoEnabled] = React.useState(
    initialUserChoices.videoEnabled
  );
  const [audioDeviceId, setAudioDeviceId] = React.useState(
    initialUserChoices.audioDeviceId
  );
  const [videoDeviceId, setVideoDeviceId] = React.useState(
    initialUserChoices.videoDeviceId
  );
  const [speakerDeviceId, setSpeakerDeviceId] = React.useState("");

  React.useEffect(() => {
    saveAudioInputEnabled(audioEnabled);
  }, [audioEnabled, saveAudioInputEnabled]);
  React.useEffect(() => {
    saveVideoInputEnabled(videoEnabled);
  }, [videoEnabled, saveVideoInputEnabled]);
  React.useEffect(() => {
    saveAudioInputDeviceId(audioDeviceId);
  }, [audioDeviceId, saveAudioInputDeviceId]);
  React.useEffect(() => {
    saveVideoInputDeviceId(videoDeviceId);
  }, [videoDeviceId, saveVideoInputDeviceId]);
  React.useEffect(() => {
    saveUsername(username);
  }, [username, saveUsername]);

  const tracks = usePreviewTracks(
    {
      audio: audioEnabled
        ? { deviceId: initialUserChoices.audioDeviceId }
        : false,
      video: videoEnabled
        ? { deviceId: initialUserChoices.videoDeviceId }
        : false,
    },
    onError,
    setToastMessage,
    setToastStatus,
    setShowToast
  );

  const videoEl = React.useRef(null);

  const videoTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Video)[0],
    [tracks]
  );

  const trackFacingMode = React.useMemo(() => {
    if (videoTrack) {
      const { facingMode } = facingModeFromLocalTrack(videoTrack);
      return facingMode;
    } else {
      return "undefined";
    }
  }, [videoTrack]);

  const audioTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Audio)[0],
    [tracks]
  );

  React.useEffect(() => {
    if (videoEl.current && videoTrack) {
      videoTrack.unmute();
      videoTrack.attach(videoEl.current);
    }

    return () => {
      videoTrack?.detach();
    };
  }, [videoTrack]);

  const handleValidation = React.useCallback(
    (values) => {
      if (typeof onValidate === "function") {
        return onValidate(values);
      } else {
        return values.username !== "";
      }
    },
    [onValidate]
  );

  React.useEffect(() => {
    const newUserChoices = {
      username,
      videoEnabled,
      videoDeviceId,
      audioEnabled,
      audioDeviceId,
    };
    setUserChoices(newUserChoices);
    setIsValid(handleValidation(newUserChoices));
  }, [
    username,
    videoEnabled,
    videoDeviceId,
    audioEnabled,
    audioDeviceId,
    handleValidation,
  ]);

  React.useEffect(() => {
    setAudioDeviceId(audioDeviceId);
    setDeviceIdAudio(audioDeviceId);
    setVideoDeviceId(videoDeviceId);
  }, [
    audioDeviceId,
    videoTrack,
    videoDeviceId,
    isVideoModalOpen
  ]);
  return (
    <div className="lk-prejoin" {...htmlProps} style={{
      position: "relative",
      backgroundColor: "#1a1a1a",
      borderRadius: "26px",
      overflow: "hidden",
      width: "100%",
      maxWidth: "1000px", /* Increased max width */
      margin: "0 auto"
    }}>
      <div
        className="mute-button"
        style={{
          position: "absolute",
          top: "15px",
          right: "15px",
          width: "40px",
          height: "40px",
          borderRadius: "50%",
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          zIndex: 10
        }}
        onClick={() => setAudioEnabled(!audioEnabled)}
      >
        {audioEnabled ? (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 14C13.66 14 15 12.66 15 11V5C15 3.34 13.66 2 12 2C10.34 2 9 3.34 9 5V11C9 12.66 10.34 14 12 14Z" fill="white"/>
            <path d="M17.91 11C17.91 11 17.83 12.95 17.09 14.12C16.32 15.33 14.95 16.22 13.5 16.53V19H10.5V16.53C9.05 16.22 7.68 15.33 6.91 14.12C6.16 12.95 6.09 11 6.09 11H4.09C4.09 11 4.19 13.36 5.13 15.03C6.08 16.7 7.83 17.9 9.8 18.29V20H7.5V22H16.5V20H14.2V18.29C16.17 17.9 17.92 16.7 18.87 15.03C19.81 13.36 19.91 11 19.91 11H17.91Z" fill="white"/>
          </svg>
        ) : (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19.91 11H17.91C17.91 11 17.83 12.95 17.09 14.12C16.32 15.33 14.95 16.22 13.5 16.53V19H10.5V16.53C9.05 16.22 7.68 15.33 6.91 14.12C6.16 12.95 6.09 11 6.09 11H4.09C4.09 11 4.19 13.36 5.13 15.03C6.08 16.7 7.83 17.9 9.8 18.29V20H7.5V22H16.5V20H14.2V18.29C16.17 17.9 17.92 16.7 18.87 15.03C19.81 13.36 19.91 11 19.91 11Z" fill="white"/>
            <path d="M15 11V5C15 3.34 13.66 2 12 2C10.34 2 9 3.34 9 5V11C9 12.66 10.34 14 12 14C13.66 14 15 12.66 15 11Z" fill="white"/>
            <path d="M2 4.27L4.28 2L22 19.72L19.73 22L15.73 18H15V14.27L8.73 8H9V11.27L2 4.27Z" fill="red"/>
          </svg>
        )}
      </div>
      <div className="lk-video-container" style={{
        backgroundColor: "#1a1a1a",
        width: "100%",
        height: "10000px", /* Increased height from 400px to 600px */
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        position: "relative"
      }}>
        {videoTrack && (
          <video
            ref={videoEl}
            width="100%"
            height="100%"
            data-lk-facing-mode={trackFacingMode}
            className={isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"}
            style={{ objectFit: "cover" }}
          >
            <track kind="captions" />
          </video>
        )}
        {(!videoTrack || !videoEnabled) && (
          <div className="lk-camera-off-note" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <div style={{
              width: '180px',
              height: '180px',
              borderRadius: '50%',
              backgroundColor: '#7e57c2',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '72px',
              fontWeight: 'bold',
              color: 'white'
            }}>
              PC
            </div>
          </div>
        )}
      </div>
      <div
        className="lk-button-group-container"
        style={{
          position: "absolute",
          bottom: "20px",
          left: "50%",
          transform: "translateX(-50%)",
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          borderRadius: "10px",
          padding: "8px 16px",
          display: "flex",
          gap: "16px",
          justifyContent: "center"
        }}
      >
        <div
          className="lk-button-group audio"
          style={{ width: "auto", justifyContent: "center" }}
        >
          <TrackToggle
            initialState={audioEnabled}
            source={Track.Source.Microphone}
            showIcon
            onChange={(enabled) => setAudioEnabled(enabled)}
            onDeviceError={onDeviceError}
            className="button-icon"
          />
          <div
            className="lk-button-group-menu"
            onClick={() => {
              setIsAudioModalOpen(!isAudioModalOpen);
              setIsVideoModalOpen(false);
            }}
          >
            <MdExpandMore />
          </div>
        </div>
        <div
          className="lk-button-group video"
          style={{ width: "auto", justifyContent: "center" }}
          >
          <TrackToggle
            initialState={videoEnabled}
            source={Track.Source.Camera}
            showIcon
            onChange={(enabled) => setVideoEnabled(enabled)}
            onDeviceError={onDeviceError}
            className="button-icon"
          />
          <div
            className="lk-button-group-menu"
            onClick={() => {
              setIsAudioModalOpen(false);
              setIsVideoModalOpen(!isVideoModalOpen);
            }}
          >
            <MdExpandMore />
          </div>
        </div>
        <div
          className="lk-button-group qr-code"
          style={{
            width: "40px",
            height: "40px",
            backgroundColor: "#181818",
            borderRadius: "10px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer"
          }}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 11H11V3H3V11ZM5 5H9V9H5V5Z" fill="#888888"/>
            <path d="M3 21H11V13H3V21ZM5 15H9V19H5V15Z" fill="#888888"/>
            <path d="M13 3V11H21V3H13ZM19 9H15V5H19V9Z" fill="#888888"/>
            <path d="M21 19H19V21H21V19Z" fill="#888888"/>
            <path d="M15 13H13V15H15V13Z" fill="#888888"/>
            <path d="M17 15H15V17H17V15Z" fill="#888888"/>
            <path d="M15 17H13V19H15V17Z" fill="#888888"/>
            <path d="M17 19H15V21H17V19Z" fill="#888888"/>
            <path d="M19 17H17V19H19V17Z" fill="#888888"/>
            <path d="M19 13H17V15H19V13Z" fill="#888888"/>
            <path d="M21 15H19V17H21V15Z" fill="#888888"/>
          </svg>
        </div>
        <div
          className="lk-button-group settings"
          style={{
            width: "40px",
            height: "40px",
            backgroundColor: "#181818",
            borderRadius: "10px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer"
          }}
          onClick={() => {
            setIsAudioModalOpen(true);
          }}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19.14 12.94C19.18 12.64 19.2 12.33 19.2 12C19.2 11.68 19.18 11.36 19.14 11.06L21.16 9.48C21.34 9.34 21.39 9.07 21.28 8.87L19.36 5.55C19.24 5.33 18.99 5.26 18.77 5.33L16.38 6.29C15.88 5.91 15.35 5.59 14.76 5.35L14.4 2.81C14.36 2.57 14.16 2.4 13.92 2.4H10.08C9.84 2.4 9.65 2.57 9.61 2.81L9.25 5.35C8.66 5.59 8.12 5.92 7.63 6.29L5.24 5.33C5.02 5.25 4.77 5.33 4.65 5.55L2.74 8.87C2.62 9.08 2.66 9.34 2.86 9.48L4.88 11.06C4.84 11.36 4.8 11.69 4.8 12C4.8 12.31 4.82 12.64 4.86 12.94L2.84 14.52C2.66 14.66 2.61 14.93 2.72 15.13L4.64 18.45C4.76 18.67 5.01 18.74 5.23 18.67L7.62 17.71C8.12 18.09 8.65 18.41 9.24 18.65L9.6 21.19C9.65 21.43 9.84 21.6 10.08 21.6H13.92C14.16 21.6 14.36 21.43 14.39 21.19L14.75 18.65C15.34 18.41 15.88 18.09 16.37 17.71L18.76 18.67C18.98 18.75 19.23 18.67 19.35 18.45L21.27 15.13C21.39 14.91 21.34 14.66 21.15 14.52L19.14 12.94ZM12 15.6C10.02 15.6 8.4 13.98 8.4 12C8.4 10.02 10.02 8.4 12 8.4C13.98 8.4 15.6 10.02 15.6 12C15.6 13.98 13.98 15.6 12 15.6Z" fill="#888888"/>
          </svg>
        </div>
      </div>

      {/* Audio Video Settings Modal */}
      {/* <AudioVideoSettingsModal
        audio={isAudioModalOpen}
        video={isVideoModalOpen}
        open={isAudioModalOpen || isVideoModalOpen}
        setOpen={isAudioModalOpen ? setIsAudioModalOpen : setIsVideoModalOpen}
        deviceId={isAudioModalOpen ? audioDeviceId : videoDeviceId}
        setDeviceId={isAudioModalOpen ? setAudioDeviceId : setVideoDeviceId}
        track={isAudioModalOpen ? audioTrack : videoTrack}
        setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
        isNoiseCancellationActive={isNoiseCancellationActive}
        setIsNoiseCancellationActive={setIsNoiseCancellationActive}
        isEchoCancellationActive={isEchoCancellationActive}
        setIsEchoCancellationActive={setIsEchoCancellationActive}
        isSelfVideoMirrored={isSelfVideoMirrored}
        setIsSelfVideoMirrored={setIsSelfVideoMirrored}
      /> */}

      {/* Audio Settings Modal */}
      <Modal
        open={isAudioModalOpen}
        onCancel={() => setIsAudioModalOpen(false)}
        footer={null}
      >
        <AudioSettings
          deviceId={audioDeviceId}
          setDeviceId={setAudioDeviceId}
          track={audioTrack}
          speakerDeviceId={speakerDeviceId}
          setSpeakerDeviceId={setSpeakerDeviceId}
          room={room}
        />
      </Modal>

      {/* Video Settings Modal */}
      <Modal
        open={isVideoModalOpen}
        onCancel={() => setIsVideoModalOpen(false)}
        footer={null}
      >
        <VideoSettings
          deviceId={videoDeviceId}
          setDeviceId={setVideoDeviceId}
          track={videoTrack}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
          // setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
          open={isVideoModalOpen}
          trackFacingMode={trackFacingMode}
        />
      </Modal>

      {/* Virtual Background Modal */}
      {/* <VirtualBackgroundModal
        open={isVisualEffectsModalOpen}
        setOpen={setIsVisualEffectsModalOpen}
        backgrounds={backgrounds}
        setBackgrounds={setBackgrounds}
        room={room}
      /> */}
    </div>
  );
}