import "@livekit/components-styles";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { useParams } from "react-router-dom";

import { LiveKitRoom } from "@livekit/components-react";
import { Room } from "livekit-client";
import { defaultUserChoices } from "@livekit/components-core";
import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";

import isElectron from "is-electron";
import { Prejoin } from "./customFabs/PreJoin";
import { VideoConference } from "./customFabs/VideoConference";

import { Loader } from "./components/Loader";
import { SettingsControlButton } from "./components/settings/SettingsControlButton";

import { PrejoinService } from "./services/PrejoinServices";
import { BreakoutRoomLoader } from "./components/BreakoutRoomLoader";
import TitleBar from "./components/titleBar";
import LoadingIcon from "./customFabs/icons/BreakoutAnimation2.json";

import { decoder, getLocalStorage, getMediaPermissions, setLocalStorage } from "./utils/helper";
import { constants, virtualBackground } from "./utils/constants";
import { ReactComponent as PlusIcon } from "./components/settings/icons/Plus.svg";

function DaakiaVC() {
  const maxHeight = Math.min(
    window.screen.height * window.devicePixelRatio,
    1620
  );
  const maxWidth = Math.min(
    window.screen.width * window.devicePixelRatio,
    2880
  );

  const [isWebinarMode, setIsWebinarMode] = useState(false);
  const [serverDetails, setServerDetails] = useState({});
  const [preJoinShow, setPreJoinShow] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [decodedId, setDecodedId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [isInvalidMeeting, setIsInvalidMeeting] = useState(false);
  const [meetingDetails, setMeetingDetails] = useState({});
  const [clientPreferedServerId, setClientPreferedServerId] = useState("ap1"); // eslint-disable-line no-unused-vars
  const [userChoices, setUserChoices] = useState({});
  const [isLobbyMode, setIsLobbyMode] = useState(false);
  const [isMeetingFinished, setIsMeetingFinished] = useState(false);
  const [isMeetingCancelled, setIsMeetingCancelled] = useState(false);
  const [isMeetingRescheduled, setIsMeetingRescheduled] = useState(false);
  const [isUsingBreakoutRoom, setIsUsingBreakoutRoom] = useState(false);
  const [isMovingToRoom, setIsMovingToRoom] = useState(false);
  const [movingRoomToken, setMovingRoomToken] = useState(null);
  const [meetingFeatures, setMeetingFeatures] = useState({});
  const [meetingUserChoices, setMeetingUserChoices] = useState({});
  const [isdataDogInitialized, setIsDataDogInitialized] = useState(false);
  const [screenShareSources, setScreenShareSources] = useState([]);
  const [isPipWindow, setIsPipWindow] = useState(false);
  const [isSelfVideoMirrored, setIsSelfVideoMirrored] = React.useState(true);
  const isElectronApp = isElectron();
  const [deviceIdAudio, setDeviceIdAudio] = useState("");
  const [deviceIdVideo, setDeviceIdVideo] = useState("");
  const [brightness, setBrightness] = useState(100);
  const [outputVolume, setOutputVolume] = useState(100);
  const [autoVideoOff, setAutoVideoOff] = useState(false);
  const [autoAudioOff, setAutoAudioOff] = useState(false);
  const [room, setRoom] = useState(
    new Room({
      dynacast: true,
      adaptiveStream: true,
      audioCaptureDefaults: {
        echoCancellation: true,
        noiseSuppression: true,
      },
      publishDefaults: {
        screenShareEncoding: {
          maxBitrate: 1_000_000,
          maxFramerate: 5,
        },
        screenShareSimulcastLayers: [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 1,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ],
      },
    })
  );
  const [isRoomFull, setIsRoomFull] = useState(false);


  const [currentEffect, setCurrentEffect] = useState(null);





  const handleBrightnessChange = async (newBrightness) => {
    setBrightness(newBrightness);


    if (room && room.localParticipant && room.state === "connected") {
      const remoteParticipants = Array.from(room.remoteParticipants.values());


      if (newBrightness !== 100 && remoteParticipants.length > 0) {

        const rpcPromises = remoteParticipants.map(async (participant) => {
          try {

            if (!participant.identity) {
              throw new Error('Participant identity is missing');
            }

            const payload = JSON.stringify({
              brightness: newBrightness,
              participantId: room.localParticipant.identity,
              timestamp: Date.now()
            });

            const response = await room.localParticipant.performRpc({
              destinationIdentity: participant.identity,
              method: "setBrightness",
              payload,
              responseTimeout: 5000
            });
            return { participant: participant.identity, success: true, response };
          } catch (error) {
            console.error(`❌ Failed to send brightness RPC to ${participant.identity}:`, error);
            return { participant: participant.identity, success: false, error: error.message };
          }
        });

        await Promise.allSettled(rpcPromises);
      }
    }
  };

  // Volume change handler (no RPC needed, local only)
  const handleOutputVolumeChange = (newVolume) => {
    setOutputVolume(newVolume);
  };

  // Auto video off change handler (no RPC needed, local only)
  const handleAutoVideoOffChange = (newAutoVideoOff) => {
    setAutoVideoOff(newAutoVideoOff);
  };

  // Auto audio off change handler (no RPC needed, local only)
  const handleAutoAudioOffChange = (newAutoAudioOff) => {
    setAutoAudioOff(newAutoAudioOff);
  };


  useEffect(() => {
    room.prepareConnection("https://ap1sfu-prod.daakia.co.in");
  }, []);


  useEffect(() => {
    if (room && room.state === "connected" && brightness !== 100) {

      const sendBrightnessToAllParticipants = async (delay = 2000) => {
        setTimeout(async () => {
          const remoteParticipants = Array.from(room.remoteParticipants.values());

          if (remoteParticipants.length > 0) {
            const rpcPromises = remoteParticipants.map(async (participant) => {

              const sendWithRetry = async (attempt = 1, maxAttempts = 3) => {
                try {

                  if (!participant.identity) {
                    throw new Error('Participant identity is missing');
                  }

                  const payload = JSON.stringify({
                    brightness,
                    participantId: room.localParticipant.identity,
                    timestamp: Date.now()
                  });

                  const response = await room.localParticipant.performRpc({
                    destinationIdentity: participant.identity,
                    method: "setBrightness",
                    payload,
                    responseTimeout: 5000
                  });
                  return { participant: participant.identity, success: true, response };
                } catch (error) {
                  console.error(`❌ Failed to send brightness RPC to ${participant.identity} on attempt ${attempt}:`, error);


                  if (attempt < maxAttempts && (error.message.includes('UNSUPPORTED_METHOD') || error.message.includes('1400'))) {
                    await new Promise((resolve) => {
                      setTimeout(() => resolve(), attempt * 2000);
                    });
                    return sendWithRetry(attempt + 1, maxAttempts);
                  }
                  return { participant: participant.identity, success: false, error: error.message };
                }
              };

              return sendWithRetry();
            });


            await Promise.allSettled(rpcPromises);
          }
        }, delay);
      };


      sendBrightnessToAllParticipants(2000);


      const handleParticipantConnected = () => {
        sendBrightnessToAllParticipants(5000);
      };

      room.on('participantConnected', handleParticipantConnected);

      return () => {
        room.off('participantConnected', handleParticipantConnected);
      };
    }
  }, [room?.state, brightness]);

  useEffect(() => {
    const item = localStorage.getItem("lk-user-choices");
    if (!item) {
      setUserChoices(defaultUserChoices);
    }
    setUserChoices(JSON.parse(item));
  }, []);

  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setDecodedId(() => decoder(id));
    }
  }, [id]);

  useEffect(() => {
    if (!isdataDogInitialized) {
      datadogLogs.init({
        clientToken: constants.DATA_DOG_TOKEN,
        site: constants.DATADOG_SITE,
        service: constants.DATADOG_LOG_SERVICE,
        env: constants.DATA_DOG_ENV,
        // version: __COMMIT_HASH__,
        forwardErrorsToLogs: false,
        sessionSampleRate: 100,
      });
      datadogRum.init({
        applicationId: constants.DATA_DOG_RUM_APPLICATION_ID,
        clientToken: constants.DATA_DOG_RUM_TOKEN,
        // `site` refers to the Datadog site parameter of your organization
        // see https://docs.datadoghq.com/getting_started/site/
        site: constants.DATADOG_SITE,
        service: constants.DATADOG_RUM_SERVICE,
        env: constants.DATA_DOG_ENV,
        // Specify a version number to identify the deployed version of your application in Datadog
        // version: '1.0.0',
        sessionSampleRate: 100,
        sessionReplaySampleRate: 100,
        trackUserInteractions: true,
        trackResources: true,
        trackLongTasks: true,
        defaultPrivacyLevel: "mask-user-input",
      });

      setIsDataDogInitialized(true);
    }
  }, []);

  // Fetch for screen share sources in desktop app
  useEffect(() => {
    const getSourcesOrError = async () => {
      try {
        const sourcesOrError = await window.electronAPI.ipcRenderer.invoke(
          "get-screenshare-sources"
        );
        if (sourcesOrError.error) {
          console.error("Error getting sources:", sourcesOrError.message);
          // Handle error (e.g., show notification, update state)
        } else {
          setScreenShareSources(sourcesOrError);
        }
      } catch (error) {
        console.error(
          "Unexpected error while fetching screen share sources:",
          error
        );
        // Handle unexpected errors
      }
    };



    if (isElectron()) {
      getSourcesOrError();
      getMediaPermissions();
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (!decodedId) return;
      try {
        const response = await PrejoinService.getMeetingDetails(decodedId);

        if (response.success === 0) {
          setIsInvalidMeeting(() => true);
          return;
        }
        setIsHost(() => response?.data?.is_host);
        const meetingData = response.data;
        const endDate = moment(meetingData?.end_date);
        const currentDate = moment();

        if (meetingData?.conference_status_id === 3 && currentDate.isAfter(endDate)) {
          setIsMeetingFinished(true);
          return;
        }else if (meetingData?.conference_status_id === 4) {
          setIsMeetingCancelled(true);
          return;
        }else if (meetingData?.conference_status_id === 5) {
          setIsMeetingRescheduled(true);
          return;
        }

        setIsPasswordProtected(() => response?.data?.is_password);
        setMeetingDetails(() => response?.data);
        setIsWebinarMode(
          () => response?.data?.event_type.toLowerCase() === "webinar"
        );
        setIsLobbyMode(() => response?.data?.is_lobby_mode);
        setLocalStorage(constants.MEETING_DETAILS, response?.data);
        const featureResponse = await PrejoinService.getMeetingFeatures(
          response?.data?.host_subscription_id || 2
        );
        if (featureResponse.success === 1) {
          setMeetingFeatures(() => featureResponse?.data);
        }
        if (
          response?.data?.meeting_logs?.session_participants >=
          Number(featureResponse?.data?.audio_video_conference)
        ) {
          setIsRoomFull(true);
        }
      } catch (error) {
        console.error("Error getting meeting details:", error);
        setIsInvalidMeeting(() => true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [decodedId]);


  useEffect(() => {
    if (!room || !isMovingToRoom || !movingRoomToken) return;
    room.disconnect();
    setServerDetails({ ...serverDetails, token: null });
    setRoom(
      new Room({
        dynacast: true,
        adaptiveStream: true,
        audioCaptureDefaults: {
          echoCancellation: true,
          noiseSuppression: true,
        },
        publishDefaults: {
          screenShareEncoding: {
            maxBitrate: 1_000_000,
            maxFramerate: 5,
          },
          screenShareSimulcastLayers: [
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 800_000,
                maxFramerate: 1,
              },
            },
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 400_000,
                maxFramerate: 1,
              },
            },
          ],
        },
      })
    );
    setServerDetails({ ...serverDetails, token: movingRoomToken });
    setMovingRoomToken(null);
    setTimeout(() => {
      setIsMovingToRoom(false);
    }, 3000);
  }, [isMovingToRoom, movingRoomToken]);

  useEffect(() => {
    if (isUsingBreakoutRoom) {
      const meetingChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
      if (meetingChoice) {
        setMeetingUserChoices(meetingChoice);
      }
    }
  }, [isUsingBreakoutRoom]);

  const [backgrounds, setBackgrounds] = useState([
    {
      heading: "Custom",
      effects: [
        {
          label: "Upload",
          icon: <PlusIcon />,
          value: "Upload",
        },
      ],
    },
    ...virtualBackground,
  ]);

  return isLoading ? (
    <div className="loader-container">
      <Loader
        heading="Please wait for a moment"
        description="We are fetching the meeting details for you."
        isLoading
      />
    </div>
  ) : isRoomFull ? (
    <div className="loader-container">
      <Loader
        heading="Room is Full!"
        description={
          isWebinarMode
            ? "The slot for this webinar is full. Please try after some time"
            : "Please contact the host for more information."
        }
        isLoading={false}
      />
    </div>
  ) : isInvalidMeeting ? (
    <div className="loader-container">
      <Loader
        heading="No Meeting Found!"
        description="Please check the meeting link and try again."
        isLoading={false}
      />
    </div>
  ) : isMeetingFinished ? (
    <div className="loader-container">
      <Loader
        heading="Meeting has been Ended!"
        description={
          isHost
            ? "You can start a new meeting."
            : "Please contact the host for more information."
        }
        isLoading={false}
      />
    </div>
  ) : isMeetingCancelled ? (
    <div className="loader-container">
      <Loader
        heading="Meeting has been Cancelled!"
        description="Please contact the host for more information."
        isLoading={false}
      />
    </div>
  ) : isMeetingRescheduled ? (
    <div className="loader-container">
      <Loader
        heading="Meeting has been Rescheduled!"
        description="Please contact the host for more information."
        isLoading={false}
      />
    </div>
  ) : isMovingToRoom ? (
    <div className="loader-container">
      <BreakoutRoomLoader
        heading="Room Hop!"
        description="Just a sec! We're moving you to your next adventure."
        isLoading
        icon={LoadingIcon}
        rootClass="breakout-room-loader"
      />
    </div>
  ) : preJoinShow ? (
    <Prejoin
      setServerDetails={setServerDetails}
      id={decodedId}
      setPreJoinShow={setPreJoinShow}
      // setIsHost={setIsHost}
      isHost={isHost}
      isPasswordProtected={isPasswordProtected}
      meetingDetails={meetingDetails}
      setClientPreferedServerId={setClientPreferedServerId}
      userChoices={userChoices}
      setUserChoices={setUserChoices}
      isLobbyMode={isLobbyMode}
      isWebinarMode={isWebinarMode}
      setIsPipWindow={setIsPipWindow}
      isPipWindow={isPipWindow}
      room={room}
      backgrounds={backgrounds}
      setBackgrounds={setBackgrounds}
      isSelfVideoMirrored={isSelfVideoMirrored}
      setIsSelfVideoMirrored={setIsSelfVideoMirrored}
      deviceIdAudio={deviceIdAudio}
      setDeviceIdAudio={setDeviceIdAudio}
      deviceIdVideo={deviceIdVideo}
      setDeviceIdVideo={setDeviceIdVideo}
      brightness={brightness}
      onBrightnessChange={handleBrightnessChange}
      outputVolume={outputVolume}
      onOutputVolumeChange={handleOutputVolumeChange}
      autoVideoOff={autoVideoOff}
      onAutoVideoOffChange={handleAutoVideoOffChange}
      autoAudioOff={autoAudioOff}
      onAutoAudioOffChange={handleAutoAudioOffChange}
      currentEffect={currentEffect}
      setCurrentEffect={setCurrentEffect}
    />
  ) : (
    !preJoinShow &&
    serverDetails.token &&
    serverDetails.serverUrl && (
      <LiveKitRoom
        room={room}
        video={
          isUsingBreakoutRoom
            ? meetingUserChoices.video
            : isWebinarMode && !isHost
            ? false
            : (() => {
                // Apply auto video off logic when joining the meeting
                const finalVideoEnabled = userChoices.autoVideoOff ? false : userChoices.videoEnabled;
                return finalVideoEnabled
                  ? { deviceId: userChoices.videoDeviceId }
                  : false;
              })()
        }
        audio={
          isUsingBreakoutRoom
            ? meetingUserChoices.audio
            : isWebinarMode && !isHost
            ? false
            : (() => {
                // Apply auto audio off logic when joining the meeting
                const finalAudioEnabled = userChoices.autoAudioOff ? false : userChoices.audioEnabled;
                return finalAudioEnabled
                  ? { deviceId: userChoices.audioDeviceId }
                  : false;
              })()
        }
        token={serverDetails.token}
        serverUrl={serverDetails.serverUrl}
        data-lk-theme="default"
        // style={{ height: "100vh" }}
      >
        {isElectronApp && (
          <TitleBar
            setIsPipWindow={setIsPipWindow}
            isPipWindow={isPipWindow}
            title={meetingDetails?.event_name}
          />
        )}
        <VideoConference
          room={room}
          SettingsComponent={SettingsControlButton}
          maxHeight={maxHeight}
          maxWidth={maxWidth}
          id={decodedId}
          isHost={isHost}
          meetingDetails={meetingDetails}
          clientPreferedServerId={clientPreferedServerId}
          isMeetingFinished={isMeetingFinished}
          setIsMeetingFinished={setIsMeetingFinished}
          setIsMovingToRoom={setIsMovingToRoom}
          setMovingRoomToken={setMovingRoomToken}
          isMovingToRoom={isMovingToRoom}
          meetingFeatures={meetingFeatures}
          isWebinarMode={isWebinarMode}
          setIsUsingBreakoutRoom={setIsUsingBreakoutRoom}
          token={serverDetails.token}
          isElectronApp={isElectronApp}
          screenShareSources={screenShareSources}
          isPipWindow={isPipWindow}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
          backgrounds={backgrounds}
          brightness={brightness}
          onBrightnessChange={handleBrightnessChange}
          outputVolume={outputVolume}
          onOutputVolumeChange={handleOutputVolumeChange}
          autoVideoOff={autoVideoOff}
          onAutoVideoOffChange={handleAutoVideoOffChange}
          autoAudioOff={autoAudioOff}
          onAutoAudioOffChange={handleAutoAudioOffChange}
          setBackgrounds={setBackgrounds}
          deviceIdAudio={deviceIdAudio}
          setDeviceIdAudio={setDeviceIdAudio}
          deviceIdVideo={deviceIdVideo}
          setDeviceIdVideo={setDeviceIdVideo}
          currentEffect={currentEffect}
          setCurrentEffect={setCurrentEffect}
        />
      </LiveKitRoom>
    )
  );
}

export default DaakiaVC;
