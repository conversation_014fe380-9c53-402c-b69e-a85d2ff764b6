.visual-effects-modal {
  // Ensure perfect centering
//   .ant-modal {
//     display: flex !important;
//     align-items: center !important;
//     justify-content: center !important;
//   }

  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    // margin: 0 auto;
    // position: relative;
    font-family: 'Inter', sans-serif;
  }

  .ant-modal-body {
    padding: 0px;
    line-height: 1.5715;
    word-wrap: break-word;
    overflow: hidden;
}

  // Hide any default modal close button
  .ant-modal-close {
    display: none !important;
  }



  .visual-effects-modal-content {
    padding: 0;
    height: auto;
    max-height: 85vh;
    min-height: 700px;
    display: flex;
    flex-direction: column;


    .modal-header {
      padding: 15px 30px 15px 30px;
      border-bottom: 1px solid #D7D7D7;
      margin-bottom: 0;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      position: relative;
      width: 100%;

      .header-back-button {
        background: none;
        border: none;
        font-size: 20px;
        color: #3b60e4;
        cursor: pointer;
        padding: 0;
        border-radius: 50%;
        transition: all 0.3s ease;
        margin-right: 12px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        line-height: 1;
        font-weight: normal;

        &:hover {
          background-color: #f0f4ff;
          transform: scale(1.1);
        }
      }

      h4 {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        padding: 0;
        color: #636363;
        flex: 1;
        line-height: 36px;
        height: 36px;
        display: flex;
        align-items: center;
      }

      .header-close-button {
        background: none;
        border: none;
        font-size: 16px;
        color: #666;
        cursor: pointer;
        padding: 0;
        border-radius: 50%;
        transition: all 0.3s ease;
        margin-left: 12px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        line-height: 1;

        &:hover {
          background-color: #f5f5f5;
          color: #333;
        }
      }
    }

    .modal-body {
      flex: 1;
      padding: 30px;
      overflow: hidden;

      .ant-row {
        height: 100%;

        .ant-col {
          height: 100%;
        }
      }
    }

    .preview-section {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      .live-preview-container {
        margin-bottom: 8px;
        flex-shrink: 0;
        text-align: center;

        .live-preview-content {
          span {

            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
          }
        }
      }

      .video-preview-container {
        width: 100%;
        aspect-ratio: 16/9;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

         max-height: 500px;

        .video-preview {
          width: 100%;
          height: 100%;
          object-fit: cover;

          &.mirrored-video {
            transform: scaleX(-1);
          }
        }

        .video-loading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 3;
          border-radius: 8px;

          .ant-spin {
            .ant-spin-dot {
              i {
                background-color: #3b60e4;
              }
            }
          }
        }

        .camera-prompt-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 5;
          border-radius: 8px;

          .camera-prompt-content {
            text-align: center;
            color: white;
            padding: 20px;
            max-width: 300px;

            .camera-prompt-message {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 8px;
              line-height: 1.4;
            }

            .camera-prompt-instruction {
              font-size: 14px;
              opacity: 0.9;
              line-height: 1.3;
            }
          }
        }

        .remove-effects-button {
          position: absolute;
          top: 12px;
          right: 12px;
          width: 40px;
          height: 40px;
          background: rgba(0, 0, 0, 0.6);
          border: none;
          border-radius: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          backdrop-filter: blur(4px);
          z-index: 10;

          svg {
            width: 20px;
            height: 20px;
          }

          // Hover effects for both states - no color changes
          &:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.05);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }

      .preview-controls {
        margin-top: 8px;
        text-align: center;
        flex-shrink: 0;

        .preview-label {
          font-size: 13px;
          color: #666;
        }
      }
    }

    .options-section {
      display: flex;
      flex-direction: column;
      height: auto;

      // Main Menu Panel
      .menu-panel {
        display: flex;
        flex-direction: column;


        .menu-grid {
          display: flex;
          flex-direction: column;
          gap: 18px;
          height: auto;

          .menu-option {
            display: flex;
            align-items: center;
            padding: 10px 8px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fff;
            height: 70px;

            &:hover {
              border-color: #3b60e4;
              box-shadow: 0 2px 8px rgba(59, 96, 228, 0.1);
              transform: translateY(-1px);
            }

            &.disabled {
              opacity: 0.6;
              cursor: not-allowed;
              pointer-events: none;
            }

            .menu-option-icon {
              width: 36px;
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #f8f9fa;
              border-radius: 6px;
              margin-right: 12px;
              flex-shrink: 0;

              svg {
                width: 18px;
                height: 18px;
                color: #3b60e4;
              }

              div {
                font-size: 16px;
              }
            }

            .menu-option-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: center;

              .menu-option-label {
                font-size: 15px;
                font-weight: 500;
                color: #1a1a1a;
                margin-bottom: 3px;
                line-height: 1.2;

                .coming-soon {
                  font-size: 12px;
                  color: #888;
                  margin-left: 8px;
                  font-style: italic;
                }
              }

              .menu-option-description {
                font-size: 12px;
                color: #666;
                line-height: 1.3;
              }
            }

            .menu-option-arrow {
              font-size: 14px;
              color: #999;
              margin-left: 8px;
              flex-shrink: 0;
            }
          }
        }
      }

      // Content Panels
      .content-panel {
        display: flex;
        flex-direction: column;
        height: 450px;
        max-height: 450px;

        // Option Header Card
        .option-header-card {
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
          flex-shrink: 0;

          .option-card {
            display: flex;
            align-items: center;
            padding: 10px 8px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            background: #E9EEFF;
            height: 70px;
            position: relative;



            .option-card-icon {
              width: 36px;
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #f8f9fa;
              border-radius: 6px;
              margin-right: 12px;
              flex-shrink: 0;

              svg {
                width: 18px;
                height: 18px;
                color: #3b60e4;
              }

              div {
                font-size: 16px;
              }
            }

            .option-card-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: center;

              .option-card-label {
                font-size: 15px;
                font-weight: 500;
                color: #1a1a1a;
                margin-bottom: 3px;
                line-height: 1.2;
              }
            }
          }
        }

        .panel-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
          flex-shrink: 0;

          .back-button {
            background: none;
            border: none;
            font-size: 14px;
            color: #3b60e4;
            cursor: pointer;
            margin-right: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.3s ease;

            &:hover {
              background-color: #f0f4ff;
            }
          }

          h5 {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: #1a1a1a;
          }
        }

        .panel-content {
          flex: 1;
          overflow-y: auto;
          max-height: 450px;

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
          }

          // Mirror control styling to match settings modal toggles
          .mirror-control {
            .control-item {
              display: flex;
              align-items: flex-start; // Align to top like other toggles
              justify-content: space-between;
              padding: 12px 10px;
              border-radius: 6px;
              background-color: rgba(255, 255, 255, 0.5);

              .control-info {
                flex: 1;
                display: flex;
                flex-direction: column;

                .control-label {
                  span {
                    font-size: 16px;
                    font-weight: 500;
                    color: #3B60E4; // Match settings modal color
                    margin-bottom: 2px;
                    line-height: 1.2;
                  }
                }

                .control-description {
                  font-size: 14px;
                  color: #555454; // Match settings modal color
                  line-height: 1.3;
                  margin: 0;
                }
              }

              // Match toggle switch size and color from settings modal
              .ant-switch {
                min-width: 36px !important; // Same as settings modal
                height: 20px !important; // Same as settings modal
                margin-left: 12px;
                margin-top: 2px; // Align with title text baseline

                .ant-switch-handle {
                  width: 16px !important; // Same as settings modal
                  height: 16px !important; // Same as settings modal
                  top: 2px !important;

                  &::before {
                    border-radius: 50% !important;
                  }
                }

                &.ant-switch-checked {
                  background-color: #3B60E4 !important; // Same blue color

                  .ant-switch-handle {
                    left: calc(100% - 18px) !important; // Same as settings modal
                  }
                }
              }
            }
          }
        }
      }

      .upload-progress {
        margin-bottom: 16px;

        .upload-status {
          display: block;
          margin-top: 8px;
          font-size: 14px;
          color: #666;
        }
      }

      .background-effects-content {
        .background-section {
          margin-bottom: 24px;

          .section-label {
            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
            margin-bottom: 12px;
          }

          .background-dropdown-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 12px;

            &:hover {
              border-color: #3b60e4;
            }

            .dropdown-arrow {
              color: #666;
              font-size: 12px;
              display: flex;
              align-items: center;

              .anticon {
                font-size: 10px;
              }
            }
          }

          .virtual-backgrounds-grid {
            // margin-top: 12px;
            // border: 1px solid #e8e8e8;
            border-radius: 6px;
            // padding: 12px;
            background: #fff;

            .vg-category-container {
            //   margin: 8px 0;
            //   padding: 8px;
              border-radius: 6px;
              border: 1px solid rgba(217, 217, 217, 0.42);
              background-color: transparent;

              .vg-heading {
                font-size: 12px;
                font-weight: 500;
                margin-bottom: 8px;
                color: #1a1a1a;
              }

              .vg-card {
                position: relative;
                cursor: pointer;
                border-radius: 6px;
                overflow: hidden;
                aspect-ratio: 16/9;
                background: #fff;
                border: 1px solid #e8e8e8;
                transition: all 0.3s ease;
                height: 50px;

                &:hover {
                  transform: scale(1.02);
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                  border-color: #3b60e4;
                }

                &.selected {
                  border-color: #3b60e4;
                  box-shadow: 0 0 0 2px rgba(59, 96, 228, 0.2);
                }

                .vg-card-image {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 16px;
                  color: #666;
                }

                .delete-bg {
                  position: absolute;
                  top: 1px;
                  left: 30px;
                  width: 18px;
                  height: 18px;
                  background: rgba(0, 0, 0, 0.5);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  cursor: pointer;
                  opacity: 0;
                  transition: opacity 0.3s ease;
                  font-size: 12px;

                  &:hover {
                    background: rgba(0, 0, 0, 0.7);
                  }
                }

                &:hover .delete-bg {
                  opacity: 1;
                }
              }
            }
          }
        }

        .blur-section {
          .section-label {
            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
            margin-bottom: 12px;
          }

          .blur-options {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;

            .ant-checkbox-wrapper {
              font-size: 14px;
              color: #1a1a1a;
              margin: 0;

              .ant-checkbox {
                .ant-checkbox-inner {
                  border-radius: 4px;
                  border-color: #e8e8e8;

                  &:hover {
                    border-color: #3b60e4;
                  }
                }

                &.ant-checkbox-checked .ant-checkbox-inner {
                  background-color: #3b60e4;
                  border-color: #3b60e4;
                }
              }

              &:hover .ant-checkbox .ant-checkbox-inner {
                border-color: #3b60e4;
              }
            }

            // Mobile responsive - stack in two lines if needed
            @media (max-width: 480px) {
              gap: 12px;

              .ant-checkbox-wrapper {
                font-size: 12px;
                flex: 0 0 calc(50% - 6px);
              }
            }
          }
        }
      }

      // Brightness Control
      .brightness-control {
        .control-label {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          font-size: 16px;
          font-weight: 500;
          color: #1a1a1a;

          svg {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            color: #3b60e4;
          }
        }

        .ant-slider {
          margin: 16px 0;

          .ant-slider-rail {
            background-color: #f0f0f0;
          }

          .ant-slider-track {
            background-color: #3b60e4;
          }

          .ant-slider-handle {
            border-color: #3b60e4;

            &:hover {
              border-color: #2a4bc7;
            }

            &:focus {
              border-color: #2a4bc7;
              box-shadow: 0 0 0 5px rgba(59, 96, 228, 0.12);
            }
          }
        }

        .brightness-value {
          text-align: center;
          font-size: 14px;
          color: #666;
          margin-top: 8px;
        }
      }

      // Mirror Control
      .mirror-control {
        .control-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px;
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          background: #fff;

          .control-info {
            flex: 1;

            .control-label {
              display: flex;
              align-items: center;
              margin-bottom: 4px;
              font-size: 16px;
              font-weight: 500;
              color: #1a1a1a;

              svg {
                width: 20px;
                height: 20px;
                margin-right: 8px;
                color: #3b60e4;
              }
            }

            .control-description {
              font-size: 14px;
              color: #666;
              line-height: 1.4;
            }
          }
        }
      }

      // Avatar Placeholder
      .avatar-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 40px 20px;

        .placeholder-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.6;
        }

        .placeholder-text {
          h6 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1a1a1a;
          }

          p {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin: 0;
            max-width: 280px;
          }
        }
      }
    }
  }
}

// Override antd modal styles for perfect centering
.ant-modal {
  .ant-modal-content {
    padding: 0;
  }
}

// Additional centering styles for the visual effects modal
.visual-effects-modal.ant-modal {
  .ant-modal-wrap {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 100vh;
  }

  .ant-modal-content {
    margin: 20px;
    max-height: calc(100vh - 40px);
    overflow: hidden;
  }
}

// Large screen specific styles to make modal bigger
@media (min-width: 1200px) {
  .visual-effects-modal {
    .visual-effects-modal-content {
      min-height: 80vh;
      max-height: 90vh;

      .preview-section {
        .video-preview-container {
          max-height: 600px;
        }
      }

      .options-section {
        .content-panel {
          height: 550px;
          max-height: 550px;

          .panel-content {
            max-height: 550px;
          }
        }
      }
    }
  }
}

// Extra large screen styles
@media (min-width: 1600px) {
  .visual-effects-modal {
    .visual-effects-modal-content {
      min-height: 85vh;
      max-height: 95vh;

      .preview-section {
        .video-preview-container {
          max-height: 700px;
        }
      }

      .options-section {
        .content-panel {
          height: 650px;
          max-height: 650px;

          .panel-content {
            max-height: 650px;
          }
        }
      }
    }
  }
}

// Tooltip styles
.ant-tooltip {
  .ant-tooltip-inner {
    background-color: #2db7f5;
    font-size: 12px;
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .visual-effects-modal {
    // Ensure proper centering on mobile
    &.ant-modal {
      .ant-modal-wrap {
        padding: 10px;
      }

      .ant-modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
        width: calc(100% - 20px) !important;
      }
    }



    .visual-effects-modal-content {
      .modal-header {
        padding: 12px 16px 0 16px;

        .header-back-button {
          font-size: 18px;
          padding: 0;
          margin-right: 8px;
          width: 32px;
          height: 32px;
          line-height: 1;
        }

        h4 {
          font-size: 16px;
          padding: 0;
          line-height: 32px;
          height: 32px;
        }

        .header-close-button {
          font-size: 14px;
          margin-left: 8px;
          width: 32px;
          height: 32px;
        }
      }

      .modal-body {
        padding: 30px;

        .preview-section {
          .video-preview-container {
            max-height: 200px;
            background: #000;

            .remove-effects-button {
              top: 8px;
              right: 8px;
              width: 32px;
              height: 32px;

              svg {
                width: 16px;
                height: 16px;
              }
            }
          }

          .preview-controls {
            margin-top: 6px;

            .preview-label {
              font-size: 12px;
            }
          }
        }

        .options-section {
          .menu-panel {
            .menu-grid {
              gap: 8px;

              .menu-option {
                height: 48px;
                padding: 8px 6px;

                .menu-option-icon {
                  width: 32px;
                  height: 32px;
                  margin-right: 10px;

                  svg {
                    width: 16px;
                    height: 16px;
                  }

                  div {
                    font-size: 14px;
                  }
                }

                .menu-option-content {
                  .menu-option-label {
                    font-size: 14px;
                    margin-bottom: 2px;
                  }

                  .menu-option-description {
                    font-size: 11px;
                  }
                }

                .menu-option-arrow {
                  font-size: 12px;
                  margin-left: 6px;
                }
              }
            }
          }

          .content-panel {
            height: 240px;
            max-height: 240px;

            .option-header-card {
              margin-bottom: 8px;
              padding-bottom: 6px;

              .option-card {
                height: 48px;
                padding: 8px 6px;



                .option-card-icon {
                  width: 32px;
                  height: 32px;
                  margin-right: 10px;

                  svg {
                    width: 16px;
                    height: 16px;
                  }

                  div {
                    font-size: 14px;
                  }
                }

                .option-card-content {
                  .option-card-label {
                    font-size: 14px;
                    margin-bottom: 2px;
                  }
                }
              }
            }

            .panel-header {
              margin-bottom: 8px;
              padding-bottom: 6px;

              h5 {
                font-size: 14px;
              }

              .back-button {
                font-size: 12px;
                margin-right: 8px;
                padding: 2px 6px;
              }
            }

            .panel-content {
              max-height: 240px;
              overflow-y: auto;
            }
          }

          .background-effects-content {
            .background-section {
              margin-bottom: 16px;

              .section-label {
                font-size: 12px;
                margin-bottom: 8px;
              }

              .background-dropdown-trigger {
                padding: 8px 10px;
                font-size: 12px;
              }

              .virtual-backgrounds-grid {
                padding: 8px;

                .vg-category-container {
                  margin: 6px 0;
                  padding: 6px;
                  border-radius: 4px;

                  .vg-heading {
                    font-size: 11px;
                    margin-bottom: 6px;
                  }

                  .vg-card {
                    height: 40px;
                    border-radius: 4px;
                    aspect-ratio: 16/9;

                    .vg-card-image {
                      font-size: 12px;
                    }

                    .delete-bg {
                      top: 2px;
                      right: 2px;
                      width: 14px;
                      height: 14px;
                      font-size: 8px;
                    }
                  }
                }
              }
            }

            .blur-section {
              .section-label {
                font-size: 12px;
                margin-bottom: 8px;
              }

              .blur-options {
                gap: 8px;

                .ant-checkbox-wrapper {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .visual-effects-modal {
    .visual-effects-modal-content {
      .modal-header {
        h4 {
          font-size: 13px;
          word-break: break-word;
          word-wrap: break-word;
          overflow-wrap: break-word;
          hyphens: auto;
          white-space: normal;
        }
      }
    }
  }
}

