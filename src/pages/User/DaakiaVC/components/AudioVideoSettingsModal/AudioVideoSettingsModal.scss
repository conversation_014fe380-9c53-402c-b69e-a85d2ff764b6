.audio-video-settings-modal {
  // margin-right: calc(100vw - 80%);
  // top: 25%;
  // width: 30rem !important;
  // position: absolute;
  // right: -105%;
  // background-color: #f4f8f9;
  // z-index: 10;
  // padding: 1rem;
  // width: 30rem;
  // height: 100%;
  // top: 0;
  // border: 1px solid black;
  // border-radius: 8px;
  .ant-modal-content{
    border-radius: 8px;
  }
  &-close{
    color: black;
    text-align: right;
    &-icon{
      cursor: pointer;
      font-size: 20px;
    }
  }
  .ant-modal-content {
    background-color: #f4f8f9;
    .ant-modal-body{
        padding: 2rem;
    }
    .ant-modal-header {
      background-color: #f4f8f9;
    }
  }
  &-switch {
    margin: 0;
  }
  .ant-switch-checked {
    background-color: #3b60e4 !important;
  }
  &-device-options {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: #a8a8a8 1px solid;
    border-radius: 3px;
    padding: 0.5rem 1rem;
    font-size: 18px;
    color: #555454;
    font-weight: 600;
    background-color: #fff;
    .ant-switch{
        margin: 0;
        &-checked {
          background-color: #3b60e4 !important;
        }
    }
  }
  &-options {
    display: flex;
    flex-direction: column;
    margin-top: 2rem;
    gap: 1rem;
    .lk-video-container{
      display: flex;
      width: 100%;
      position: relative;
      video{
        border-radius: 8px;
        border: none; // Remove the light border
      }
    }
    h4{
      font-weight: 600;
      font-size: 20px;
      color: #555454;
    }
  }
  &-option {
    display: flex;
    gap: 1rem;
    align-items: center;
    .visual-effects{
      cursor: pointer;
    }
    &-switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      border: 1px solid #a8a8a8;
      border-radius: 3px;
      padding: 0.5rem 1rem;
      background-color: #fff;
    }
    &-dropdown {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      position: relative;
      background-color: #fff;
      .lk-device-menu {
        background-color: #fff;
        width: 100%;
        left: 0rem !important;
        top: 2.9rem !important;
        .lk-media-device-select {
          list-style: none;
          padding: 0;
          margin: 0;
          li {
            overflow: auto;
            button {
              font-weight: 600;
            }
            &:hover {
              background-color: #f4f8f9;
            }
          }
        }
      }
    }
  }
  &-icon {
    background: rgb(59, 96, 228);
    background: linear-gradient(
      90deg,
      rgba(59, 96, 228, 1) 0%,
      rgba(86, 123, 255, 1) 100%
    );
    border-radius: 50%;
    width: 37px;
    height: 35px;
    padding: 5px;
  }
}
.device-name {
  font-weight: 600;
  font-size: 18px;
  color: #555454;
  font-family: "Inter";
}

.visual-effects-modal {
  // width: 30rem !important;
  .ant-modal-content{
    border-radius: 8px !important;
  }
  .ant-modal-body {
    background-color: #f4f8f9;
    padding: 2rem;
    padding-top: 3.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  &-options {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
    h4{
      font-weight: 600;
      font-size: 20px;
      color: #555454;
    }
    &-dropdown{
      display: flex;
      justify-content: space-between;
      align-items: center;
      // border: 1px solid #a8a8a8;
      border-radius: 3px;
      width: 100%;
      position: relative;
    }
    &:first-child {
      margin-bottom: 0.5rem;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
    .visual-effects{
      display: flex;
      border: 1px solid #a8a8a8;
      border-radius: 3px;
      padding: 0.5rem 1rem;
      background-color: #fff;
      font-size: 18px;
      font-weight: 600;
      color: #555454;
      cursor: pointer;
    }
    &-select {
      width: 17rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-option {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }
    }
  }
}
.virtual-background-popover{
  width: 17rem;
  .ant-popover-inner-content{
    width: 100%;
    height: 20rem;
    overflow: auto;
  }
}

.custom-dropdown-trigger {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
}

.custom-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
}

.custom-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    background-color: #f5f5f5;
  }

  .selected-icon {
    color: #1890ff;
  }
}