import { <PERSON>, <PERSON><PERSON>, <PERSON>, Spin } from "antd";
import React, { useEffect, useRef, useState } from "react";
import "./VirtualBackgroundModal.scss";
import axios from "axios";
import { datadogLogs } from "@datadog/browser-logs";
import { CloseOutlined } from "@ant-design/icons";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
// import { noEffect } from "../../utils/virtualBackground";
import { constants } from "../../utils/constants";
import * as routes from "../../API/Endpoints/routes";
import { getLocalStorageToken } from "../../utils/helper";
import { ReactComponent as BackgroundEffectsIcon } from "../../assets/icons/Background_Effects.svg";
import { ReactComponent as AdjustBrightnessIcon } from "../../assets/icons/Adjust_brightness.svg";
import { ReactComponent as ChooseAvatarIcon } from "../../assets/icons/Choose_Avatar.svg";
import { ReactComponent as MirrorViewIcon } from "../../assets/icons/Mirror_view.svg";
import { ReactComponent as RemoveEffectsIcon } from "./Assets/removeEffects.svg";
import { ReactComponent as BackButtonIcon } from "./Assets/backbutton.svg";
import {
  BackgroundEffectsPanel,
  BrightnessPanel,
  MirrorViewPanel,
  AvatarPanel
} from "./components/virtualBackgroundModal";

export default function VirtualBackgroundModal({
  open,
  setOpen,
  backgrounds,
  setBackgrounds,
  onEffectSelected,
  videoTrack,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  currentEffect,
  brightness = 100,
  onBrightnessChange,
  hasCameraBeenEnabledOnce = false,
  videoEnabled = false,
  setVideoEnabled,
}) {
  const videoRef = useRef(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [activeTab, setActiveTab] = useState(null);
  const [selectedBackground, setSelectedBackground] = useState(null);
  const [showBackgroundGrid, setShowBackgroundGrid] = useState(false);
  const [blurOptions, setBlurOptions] = useState({
    noBlur: true,
    lightBlur: false,
    heavyBlur: false
  });
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [originalVideoState, setOriginalVideoState] = useState(null);

  useEffect(() => {
    let mounted = true;
    let cleanupEventListeners = null;

    const setupVideo = async () => {
      if (!videoTrack || !videoRef.current) {
        setIsVideoLoading(false);
        return;
      }

      try {
        setIsVideoLoading(true);

        // Ensure the track is enabled and unmuted
        if (!videoTrack.isEnabled) {
          await videoTrack.enable();
        }
        if (videoTrack.isMuted) {
          await videoTrack.unmute();
        }

        // Attach the track to the video element
        videoTrack.attach(videoRef.current);

        // Add event listeners for video loading states
        const videoElement = videoRef.current;

        const handleLoadedData = () => {
          if (mounted) {
            setIsVideoLoading(false);
          }
        };

        const handleCanPlay = () => {
          if (mounted) {
            setIsVideoLoading(false);
          }
        };

        const handleLoadStart = () => {
          if (mounted) {
            setIsVideoLoading(true);
          }
        };

        videoElement.addEventListener('loadeddata', handleLoadedData);
        videoElement.addEventListener('canplay', handleCanPlay);
        videoElement.addEventListener('loadstart', handleLoadStart);

        // Store cleanup function
        cleanupEventListeners = () => {
          videoElement.removeEventListener('loadeddata', handleLoadedData);
          videoElement.removeEventListener('canplay', handleCanPlay);
          videoElement.removeEventListener('loadstart', handleLoadStart);
        };

        // Start playing the video
        if (videoElement) {
          await videoElement.play().catch(console.error);
        }
      } catch (error) {
        console.error('Error setting up video preview:', error);
        if (mounted) {
          setIsVideoLoading(false);
        }
      }
    };

    if (mounted && open) {
      setupVideo();
    } else {
      setIsVideoLoading(false);
    }

    return () => {
      mounted = false;
      if (cleanupEventListeners) {
        cleanupEventListeners();
      }
      if (videoRef.current) {
        // Clean up the video element
        const videoElement = videoRef.current;
        if (videoElement.srcObject) {
          videoElement.srcObject = null;
        }
      }
    };
  }, [videoTrack, open, currentEffect]);

  const fetchVirtualBackgrounds = async () => {
    try {
      const response = await SettingsMenuServices.getVirtualBackground();
      if (response.success === 1) {
        const { data } = response;
        const custom = backgrounds.find(
          (category) => category.heading === "Custom"
        );
        data.forEach((item, index) => {
          if (item.id !== null) {
            const isUrlPresent = custom.effects.some(
              (effect) => effect.icon === item.url
            );
            if (!isUrlPresent) {
              custom.effects.unshift({
                label: `Custom ${index + 1}`,
                icon: item.url,
                value: `CT_${index + 1}`,
                id: item.id,
              });
            }
          }
        });
        setBackgrounds([...backgrounds]);
      }
    } catch (error) {
      console.error("Error fetching virtual backgrounds", error);
    }
  };

  useEffect(() => {
    fetchVirtualBackgrounds();
  }, []);

  // Reset to main menu when modal opens and store original video state
  useEffect(() => {
    if (open) {
      setActiveTab(null);
      // Store the original video state when modal opens
      if (originalVideoState === null) {
        setOriginalVideoState(videoEnabled);
      }
    } else {
      // Reset original video state when modal closes
      setOriginalVideoState(null);
    }
  }, [open, videoEnabled, originalVideoState]);

  // Sync blur options and selected background with current effect
  useEffect(() => {
    if (currentEffect) {
      if (currentEffect.type === 'blur') {
        const newBlurOptions = {
          noBlur: false,
          lightBlur: false,
          heavyBlur: false
        };

        if (currentEffect.value === 5) {
          newBlurOptions.lightBlur = true;
        } else if (currentEffect.value === 20) {
          newBlurOptions.heavyBlur = true;
        }

        setBlurOptions(newBlurOptions);
        setSelectedBackground(null); // Clear background selection when blur is active
      } else if (currentEffect.type === 'background') {
        // Find the background name for the current effect
        let foundBackground = null;
        for (const category of backgrounds) {
          const effect = category.effects.find(e =>
            e.value === currentEffect.value || e.icon === currentEffect.value
          );
          if (effect) {
            foundBackground = effect.label;
            break;
          }
        }
        setSelectedBackground(foundBackground);

        // Clear blur options when background is active
        setBlurOptions({
          noBlur: false,
          lightBlur: false,
          heavyBlur: false
        });
      } else if (currentEffect.type === 'none') {
        // If current effect is explicitly 'none', set no blur as selected
        setBlurOptions({
          noBlur: true,
          lightBlur: false,
          heavyBlur: false
        });
        setSelectedBackground(null);
      }
    } else {
      // No current effect at all - clear everything (truly no effects)
      setBlurOptions({
        noBlur: false,
        lightBlur: false,
        heavyBlur: false
      });
      setSelectedBackground(null);
    }
  }, [currentEffect, backgrounds]);

  // Apply brightness effect to modal video element when brightness changes
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.style.filter = `brightness(${brightness}%)`;
    }
  }, [brightness]);

  const handleUpload = async () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";

    fileInput.onchange = async (event) => {
      const file = event.target.files[0];

      if (file) {
        try {
          setIsUploading(true);
          setUploadProgress(0);

          const formData = new FormData();
          formData.append("image", file);

          const response = await axios.post(
            `${constants.STAG_BASE_URL}${routes.Endpoints.set_virtual_background.url}`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                Authorization: `${getLocalStorageToken()}`,
              },
              onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
                setUploadProgress(percentCompleted);
              },
            }
          );

          if (response.data.success === 0) {
            datadogLogs.logger.error("Error in uploading virtual background", {
              response,
              payload: file,
              endpoint: routes.Endpoints.set_virtual_background.url,
            });
            return;
          }

          datadogLogs.logger.info("Success in uploading virtual background", {
            response,
            payload: file,
            endpoint: routes.Endpoints.set_virtual_background.url,
          });

          const uploadedImageUrl = response.data.data?.url;

          if (!uploadedImageUrl) {
            console.error("No URL received from server");
            return;
          }

          for (const category of backgrounds) {
            if (category.heading === "Custom") {
              category.effects.unshift({
                label: file.name,
                icon: uploadedImageUrl,
                value: `CT_${category.effects.length + 1}`,
                id: response.data.data?.id,
              });
            }
          }
          setBackgrounds([...backgrounds]);
        } catch (error) {
          console.error("Error uploading virtual background:", error);
          datadogLogs.logger.error("Error in uploading virtual background", {
            error,
            payload: file,
            endpoint: routes.Endpoints.set_virtual_background.url,
          });
        } finally {
          setIsUploading(false);
          setUploadProgress(0);
        }
      }
    };

    fileInput.click();
  };

  const handleDelete = async (virtualBackgroundId) => {
    virtualBackgroundId = parseInt(virtualBackgroundId);

    try {
      await axios.delete(
        `${constants.STAG_BASE_URL}${routes.Endpoints.delete_virtual_background.url}`,
        {
          data: {
            virtual_background_id: virtualBackgroundId,
          },
          headers: {
            Authorization: `${getLocalStorageToken()}`,
          },
        }
      );

      for (const category of backgrounds) {
        if (category.heading === "Custom") {
          category.effects = category.effects.filter(
            (effect) => effect.id !== virtualBackgroundId
          );
        }
      }
      setBackgrounds([...backgrounds]);
    } catch (error) {
      console.error("Error deleting virtual background", error);
    }
  };

  const handleEffectClick = (_, value) => {
    if (onEffectSelected) {
      if (value === 0) {
        onEffectSelected('none', 0);
      } else if (value === "Upload") {
        handleUpload();
      } else if (typeof value === "string" && value?.startsWith("CT_")) {
        onEffectSelected('background', value);
      } else if (typeof value === "number") {
        onEffectSelected('blur', value);
      } else {
        onEffectSelected('background', value);
      }
    }
  };

  const handleClose = () => {
    // Mute the video track if it exists and we're restoring to original state (off)
    if (videoTrack && originalVideoState === false) {
      videoTrack.mute();
    }
    
    // Restore camera to its original state when modal closes
    if (setVideoEnabled && originalVideoState !== null) {
      setVideoEnabled(originalVideoState);
    }
    setOpen(false);
  };

  // Menu options configuration
  const menuOptions = [
    {
      key: 'backgrounds',
      label: 'Background & Effects',
      icon: <BackgroundEffectsIcon />
    },
    {
      key: 'brightness',
      label: 'Adjust brightness',
      icon: <AdjustBrightnessIcon />
    },
    {
      key: 'mirror',
      label: 'Mirror view',
      icon: <MirrorViewIcon />
    },
    {
      key: 'avatar',
      label: 'Choose Avatar',
      icon: <ChooseAvatarIcon />,
      disabled: true,
      comingSoon: true
    }
  ];

  const handleMenuClick = (key) => {
    setActiveTab(key);
  };

  const handleMirrorToggle = () => {
    if (setIsSelfVideoMirrored) {
      setIsSelfVideoMirrored(!isSelfVideoMirrored);
    }
  };

  const handleBrightnessChange = (value) => {
    // Call the parent component's brightness change handler
    if (onBrightnessChange) {
      onBrightnessChange(value);
    }
    // Apply brightness filter to video element in modal
    if (videoRef.current) {
      videoRef.current.style.filter = `brightness(${value}%)`;
    }
  };

  const handleBlurChange = (blurType, checked) => {
    // Reset all blur options
    const newBlurOptions = {
      noBlur: false,
      lightBlur: false,
      heavyBlur: false
    };

    // Set the selected blur option
    if (checked) {
      newBlurOptions[blurType] = true;

      // Apply the blur effect
      if (blurType === 'noBlur') {
        handleEffectClick('none', 0);
      } else if (blurType === 'lightBlur') {
        handleEffectClick('blur', 5);
      } else if (blurType === 'heavyBlur') {
        handleEffectClick('blur', 20);
      }
    }

    setBlurOptions(newBlurOptions);
  };

  const handleRemoveEffects = () => {
    // Remove all virtual background effects
    if (onEffectSelected) {
      onEffectSelected(null);
    }
    // Reset selected background
    setSelectedBackground(null);
    // Clear all blur options (truly no effects)
    setBlurOptions({
      noBlur: false,
      lightBlur: false,
      heavyBlur: false
    });
    // Reset brightness filter on video element
    if (videoRef.current) {
      videoRef.current.style.filter = 'brightness(100%)';
    }
  };

  // Helper function to determine if any effects are active
  const hasAnyEffects = () => {
    // Check if there's a virtual background effect (must have valid type that's not 'none' or null)
    const hasVBG = currentEffect && currentEffect.type && currentEffect.type !== 'none';
    // Check if any blur is active (light or heavy blur, NOT no blur)
    const hasBlur = blurOptions.lightBlur || blurOptions.heavyBlur;

    return hasVBG || hasBlur;
  };

  // Render option header card component
  const renderOptionHeader = (option) => {
    return (
      <div className="option-header-card">
        <div className="option-card">
          <div className="option-card-icon">
            {option.icon}
          </div>
          <div className="option-card-content">
            <div className="option-card-label">{option.label}</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Modal
      open={open}
      onOk={handleClose}
      onCancel={handleClose}
      className="visual-effects-modal"
      footer={null}
      width="90%"
      style={{ maxWidth: '1000px' }}
      closeIcon={null}
      centered
    >
      <div className="visual-effects-modal-content">
        {/* Header Section */}
        <div className="modal-header">
          {activeTab && (
            <button
              className="header-back-button"
              onClick={() => setActiveTab(null)}
            >
              <BackButtonIcon />
            </button>
          )}
          <h4>
            {activeTab
              ? menuOptions.find(opt => opt.key === activeTab)?.label
              : 'Virtual Background & Effects'
            }
          </h4>
          <button
            className="header-close-button"
            onClick={handleClose}
          >
            <CloseOutlined />
          </button>
        </div>

        {/* Body Section */}
        <div className="modal-body">
          <Row gutter={[24, 16]} style={{ height: '100%' }}>
            {/* Left Column - Video Preview */}
            <Col
              xs={24}
              sm={24}
              md={14}
              lg={16}
              xl={16}
              style={{ marginBottom: { xs: 16, sm: 16, md: 0 } }}
            >
              <div className="preview-section ">

                <div className="video-preview-container">
                  <video
                    ref={videoRef}
                    className={`video-preview ${isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"}`}
                    autoPlay
                    playsInline
                    muted
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '8px'
                    }}
                  />

                  {/* Video Loading Spinner */}
                  {isVideoLoading && (
                    <div className="video-loading-overlay">
                      <Spin size="large" />
                    </div>
                  )}

                  {/* Camera Toggle Prompt - Show when camera hasn't been enabled at least once */}
                  {!hasCameraBeenEnabledOnce && (
                    <div className="camera-prompt-overlay">
                      <div className="camera-prompt-content">
                        <div className="camera-prompt-message">
                          Please toggle your camera at least once to enable all features
                        </div>
                        <div className="camera-prompt-instruction">
                          Use the camera button in the prejoin controls
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Remove Effects Button - Always visible, changes icon based on VBG state */}
                  <button
                    className={`remove-effects-button ${hasAnyEffects() ? 'has-effect' : 'no-effect'}`}
                    onClick={handleRemoveEffects}
                    title={hasAnyEffects() ? "Remove all effects" : "No effects applied"}
                    style={{
                      color: hasAnyEffects() ? '#3b60e4' : '#ffffff'
                    }}
                  >
                    <RemoveEffectsIcon />
                  </button>
                </div>


              </div>
            </Col>

            {/* Right Column - Menu and Content */}
            <Col
              xs={24}
              sm={24}
              md={10}
              lg={8}
              xl={8}
            >
              <div className="options-section">
                {activeTab === 'backgrounds' ? (
                  // Background & Effects Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'backgrounds')
                    )}
                    <BackgroundEffectsPanel
                      isUploading={isUploading}
                      uploadProgress={uploadProgress}
                      showBackgroundGrid={showBackgroundGrid}
                      setShowBackgroundGrid={setShowBackgroundGrid}
                      selectedBackground={selectedBackground}
                      backgrounds={backgrounds}
                      currentEffect={currentEffect}
                      handleUpload={handleUpload}
                      handleEffectClick={handleEffectClick}
                      setSelectedBackground={setSelectedBackground}
                      handleDelete={handleDelete}
                      blurOptions={blurOptions}
                      handleBlurChange={handleBlurChange}
                    />
                  </div>
                ) : activeTab === 'brightness' ? (
                  // Brightness Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'brightness')
                    )}
                    <BrightnessPanel
                      brightness={brightness}
                      handleBrightnessChange={handleBrightnessChange}
                    />
                  </div>
                ) : activeTab === 'mirror' ? (
                  // Mirror View Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'mirror')
                    )}
                    <MirrorViewPanel
                      isSelfVideoMirrored={isSelfVideoMirrored}
                      handleMirrorToggle={handleMirrorToggle}
                    />
                  </div>
                ) : activeTab === 'avatar' ? (
                  // Avatar Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'avatar')
                    )}
                    <AvatarPanel />
                  </div>
                ) : (
                  // Main Menu
                  <div className="menu-panel">
                    <div className="menu-grid">
                      {menuOptions.map((option) => (
                        <div
                          key={option.key}
                          className={`menu-option ${option.disabled ? 'disabled' : ''}`}
                          onClick={() => !option.disabled && handleMenuClick(option.key)}
                        >
                          <div className="menu-option-icon">
                            {option.icon}
                          </div>
                          <div className="menu-option-content">
                            <div className="menu-option-label">
                              {option.label}
                              {option.comingSoon && <span className="coming-soon">Coming Soon</span>}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </Modal>
  );
}

