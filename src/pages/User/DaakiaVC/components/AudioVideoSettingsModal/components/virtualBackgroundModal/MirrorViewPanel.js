import React from 'react';
import Switch from '../../../../../../../components/Antd/Switch/index.ant';

function MirrorViewPanel({
  isSelfVideoMirrored,
  handleMirrorToggle
}) {
  return (
    <div className="panel-content px-2">
      <div className="mirror-control">
        <div className="control-item">
          <div className="control-info">
            <div className="control-label">
            
              <span>Mirror Video</span>
            </div>
            <div className="control-description">
              Mirror your video horizontally for others to see
            </div>
          </div>
          <Switch
            checked={isSelfVideoMirrored}
            onChange={handleMirrorToggle}
          />
        </div>
      </div>
    </div>
  );
}

export default MirrorViewPanel;
