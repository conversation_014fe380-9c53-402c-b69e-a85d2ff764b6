import React from 'react';

/**
 * MicLevelIndicator Component
 * Displays audio level with rectangular bars
 * @param {number} level - Audio level (0-100)
 * @param {boolean} isActive - Whether the indicator should show active state
 */
function MicLevelIndicator({ level, isActive = false }) {
  const numberOfBars = 7;
  const activeBars = Math.ceil((level / 100) * numberOfBars);

  return (
    <div className="mic-level-indicator">
      {Array.from({ length: numberOfBars }, (_, index) => {
        const isBarActive = isActive && index < activeBars;

        return (
          <div
            key={index}
            className={`mic-bar ${isBarActive ? 'active' : ''}`}
            style={{
              height: '24px', // Same height for all bars (rectangular)
              flex: 1,  // Take equal share of available width
              backgroundColor: isBarActive ? '#3B60E4' : '#e8e8e8',
              borderRadius: '2px',
              transition: 'background-color 0.1s ease-in-out'
            }}
          />
        );
      })}
    </div>
  );
}

export default MicLevelIndicator;
