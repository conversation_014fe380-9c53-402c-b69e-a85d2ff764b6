import React from 'react';
import DeviceDropdown from './DeviceDropdown';

/**
 * CameraSettings Component
 * Camera device selection section
 * @param {Array} videoDevices - Available video input devices
 * @param {string} videoDeviceId - Selected video device ID
 * @param {function} onVideoDeviceChange - Video device change handler
 * @param {boolean} permissions - Camera permission status
 */
function CameraSettings({
  videoDevices = [],
  videoDeviceId,
  onVideoDeviceChange,
  permissions = { camera: false }
}) {
  return (
    <div className="settings-section camera-settings-section">
      <div className="grid-container camera-grid">
        <div className="grid-cell left">
          <span className="setting-label">Camera</span>
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Choose Camera</span>
        </div>
        <div className="grid-cell center">
          <DeviceDropdown
            devices={videoDevices}
            selectedDeviceId={videoDeviceId}
            onDeviceChange={onVideoDeviceChange}
            hasPermission={permissions.camera}
            permissionMessage="Grant camera permission to see devices"
            noDevicesMessage="No cameras found"
            defaultDeviceName="Default Camera"
            selectPlaceholder="Select camera"
          />
        </div>
      </div>
    </div>
  );
}

export default CameraSettings;
