import React from 'react';
import { Switch, Slider } from 'antd';

/**
 * VideoEnhancementSettings Component
 * Video enhancement controls (mirror video, brightness, auto video off)
 * @param {boolean} isSelfVideoMirrored - Mirror video state
 * @param {function} onMirrorVideoChange - Mirror video change handler
 * @param {number} brightness - Video brightness value (50-150)
 * @param {function} onBrightnessChange - Brightness change handler
 * @param {boolean} autoVideoOff - Auto video off joining meeting state
 * @param {function} onAutoVideoOffChange - Auto video off change handler
 */
function VideoEnhancementSettings({
  isSelfVideoMirrored = false,
  onMirrorVideoChange,
  brightness = 100,
  onBrightnessChange,
  autoVideoOff = false,
  onAutoVideoOffChange
}) {
  return (
    <div className="settings-section">
      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Mirror video</span>
            <span className="setting-sublabel">Flip your video horizontally to correct the orientation</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={isSelfVideoMirrored}
            onChange={onMirrorVideoChange}
          />
          <span className="switch-status">
            {isSelfVideoMirrored ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
      <div className="setting-border" />

      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Video brightness</span>
            <span className="setting-sublabel">Adjust the brightness of your video to improve visibility</span>
          </div>
        </div>
        <div className="setting-control">
          <Slider
            value={brightness}
            onChange={onBrightnessChange}
            min={50}
            max={150}
            tooltip={{ formatter: (value) => `${value}%` }}
          />
          <span className="brightness-value">{brightness}%</span>
        </div>
      </div>
      <div className="setting-border" />

      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Auto video off on joining meeting</span>
            <span className="setting-sublabel">Automatically turn off your camera when joining the meeting</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={autoVideoOff}
            onChange={onAutoVideoOffChange}
          />
          <span className="switch-status">
            {autoVideoOff ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
    </div>
  );
}

export default VideoEnhancementSettings;
