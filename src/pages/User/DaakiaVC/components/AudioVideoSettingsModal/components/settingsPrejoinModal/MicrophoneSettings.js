import React from 'react';
import DeviceDropdown from './DeviceDropdown';
import TestButton from './TestButton';
import MicLevelIndicator from './MicLevelIndicator';

/**
 * MicrophoneSettings Component
 * Complete microphone configuration section
 * @param {Array} audioDevices - Available audio input devices
 * @param {string} audioDeviceId - Selected audio device ID
 * @param {function} onAudioDeviceChange - Audio device change handler
 * @param {boolean} permissions - Microphone permission status
 * @param {function} onTestMicrophone - Microphone test handler
 * @param {boolean} isMicTesting - Whether microphone is being tested
 * @param {boolean} audioEnabled - Whether audio is enabled
 * @param {object} micOriginalState - Original microphone state
 * @param {number} currentAudioLevel - Current audio level (0-100)
 */
function MicrophoneSettings({
  audioDevices = [],
  audioDeviceId,
  onAudioDeviceChange,
  permissions = { microphone: false },
  onTestMicrophone,
  isMicTesting = false,
  audioEnabled = false,
  micOriginalState = null,
  currentAudioLevel = 0
}) {
  // Determine button text and state
  const getTestButtonText = () => {
    if (isMicTesting) {
      return 'Stop Test';
    }

    if (!audioEnabled && micOriginalState === null) {
      return 'Enable & Test Mic';
    }

    return 'Test Mic';
  };

  return (
    <div className="settings-section microphone-settings-section">
      <div className="grid-container microphone-grid">
        <div className="grid-cell left">
          <span className="setting-label">Microphone</span>
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Choose Microphone</span>
        </div>
        <div className="grid-cell center">
          <DeviceDropdown
            devices={audioDevices}
            selectedDeviceId={audioDeviceId}
            onDeviceChange={onAudioDeviceChange}
            hasPermission={permissions.microphone}
            permissionMessage="Grant microphone permission to see devices"
            defaultDeviceName="Default Microphone"
            selectPlaceholder="Select microphone"
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Test Mic</span>
        </div>
        <div className="grid-cell left">
          <TestButton
            onClick={onTestMicrophone}
            isActive={isMicTesting}
            activeText="Stop Test"
            inactiveText={getTestButtonText()}
            disabled={!permissions.microphone}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Input Level:</span>
        </div>
        <div className="grid-cell center">
          <MicLevelIndicator
            level={currentAudioLevel}
            isActive={isMicTesting && currentAudioLevel > 0}
          />
        </div>
      </div>
    </div>
  );
}

export default MicrophoneSettings;
