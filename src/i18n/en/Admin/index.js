export { appLanguage as master } from "./Master/index.i18n";
export { appLanguage as pendingQueries } from "./ContactUs/PendingQueries/index.i18n";
export { appLanguage as userManagement } from "./UserManagement/index.i18n";
export { appLanguage as AdminUpdateSocialLink } from "./AdminUpdateSocialLink/index.i18n";
export { appLanguage as adminUpdateProfile } from "./AdminUpdateProfile/index.i18n";
export { appLanguage as discountRequest } from "./DiscountRequest/index.i18n";
export { appLanguage as transactionHistory } from "./TransactionHistory/index.i18n";
export { appLanguage as faqs } from "./ManageCms/FAQs/index.i18n";
export { appLanguage as manageCmsList } from "./ManageCms/ManageCmsList/index.i18n";
export { appLanguage as privacyPolicy } from "./ManageCms/PrivacyPolicy/index.i18n";
export { appLanguage as termsAndCondition } from "./ManageCms/TermsAndCondition/index.i18n";
export { appLanguage as cookiesPolicy } from "./ManageCms/CookiesPolicy/index.i18n";
export { appLanguage as newsletterSubscribers } from "./NewsletterSubscriber/index.i18n";
export { appLanguage as manageClientsPartners } from "./ManageClientPartner/index.i18n";
export { appLanguage as manageTeamMembers } from "./ManageTeamMember/index.i18n";
export { appLanguage as manageTaxes } from "./ManageTaxes/index.i18n";
export { appLanguage as referralAdmin } from "./Referrals/index.i18n";
