<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Secure & high quality meetings powered by <PERSON>akia!"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- OG Properties -->
    <meta property="og:title" content="Daakia" />
    <meta
      property="og:description"
      content="Secure & high quality meetings powered by <PERSON>aki<PERSON>!"
    />
    <meta
      property="og:image"
      content="https://daakia-dev-media.daakia.co.in/profile-image/Invite_Referral01-comp_1702293470473.png"
    />

    <!--for recaptcha security error-->
    <!-- <meta http-equiv="Content-Security-Policy" content="script-src 'self' https://www.google.com https://www.gstatic.com https://www.googletagmanager.com https://sdk.cashfree.com 'unsafe-inline';"> -->

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Daakia</title>

    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-P36QTMMF");
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body>

    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-P36QTMMF"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
     
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-ND8NPKS"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- <script src="https://sdk.cashfree.com/js/ui/2.0.0/cashfree.sandbox.js"></script> -->
    <!-- <script src="https://sdk.cashfree.com/js/ui/2.0.0/cashfree.prod.js"></script> -->
    <script src="%REACT_APP_CASHFREE_URL%"></script>
    <script>
      // Disable Auto Zoom function ios
      var iosDevice =
        navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform);
      if (iosDevice)
        document.head.querySelector('meta[name="viewport"]').content =
          "width=device-width, initial-scale=1, maximum-scale=1";
      else
        document.head.querySelector('meta[name="viewport"]').content =
          "width=device-width, initial-scale=1";
    </script>
  </body>
</html>
