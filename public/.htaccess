Options -MultiViews
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.html [QSA,L]

    RewriteCond %{HTTPS} !=on
    RewriteRule ^/?(.*) https://%{SERVER_NAME} [R,L]

    # https://help.dreamhost.com/hc/en-us/articles/216363157-How-can-I-cache-my-site-with-an-htaccess-file-
    # BEGIN Expire headers  
    <IfModule mod_expires.c>  
        # Turn on the module.
        ExpiresActive on
        # Set the default expiry times.
        ExpiresDefault "access plus 2 days"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/svg+xml "access 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType text/javascript "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType application/x-shockwave-flash "access plus 1 month"
        ExpiresByType image/ico "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 month"
        ExpiresByType text/html "access plus 600 seconds"
    </IfModule>  
    # END Expire headers 