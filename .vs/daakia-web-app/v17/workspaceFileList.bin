      1@n  D:\daakia-web-app\.eslintrc.js                           D:\daakia-web-app\.gitignore                           "D:\daakia-web-app\public\.htaccess                           9D:\daakia-web-app\src\styles\images\frontend\about-bg.jpg                           =D:\daakia-web-app\public\assets\images\frontend\about-img.png                           ?D:\daakia-web-app\src\styles\images\frontend\about-right-bg.jpg                           2D:\daakia-web-app\src\styles\scss\admin\admin.scss                           1D:\daakia-web-app\src\routeControl\adminRoutes.js                           ID:\daakia-web-app\src\styles\scss\admin\components\ant-form\ant-form.scss                           MD:\daakia-web-app\src\styles\scss\admin\components\ant-picker\ant-picker.scss                           >D:\daakia-web-app\public\assets\images\frontend\app-screen.png                           ?D:\daakia-web-app\public\assets\images\frontend\app-screen.webp                           =D:\daakia-web-app\public\assets\images\frontend\app-store.png                           D:\daakia-web-app\src\App.css                           D:\daakia-web-app\src\App.js                           !D:\daakia-web-app\src\App.test.js                           JD:\daakia-web-app\src\styles\fonts\frontend\ArchitectsDaughter-Regular.eot                           JD:\daakia-web-app\src\styles\fonts\frontend\ArchitectsDaughter-Regular.svg                           JD:\daakia-web-app\src\styles\fonts\frontend\ArchitectsDaughter-Regular.ttf                           KD:\daakia-web-app\src\styles\fonts\frontend\ArchitectsDaughter-Regular.woff                           LD:\daakia-web-app\src\styles\fonts\frontend\ArchitectsDaughter-Regular.woff2                           GD:\daakia-web-app\src\styles\images\frontend\audio-video-conference.jpg                           ?D:\daakia-web-app\public\assets\images\frontend\audio-video.svg                           8D:\daakia-web-app\src\styles\images\frontend\auth-bg.jpg                           :D:\daakia-web-app\public\assets\images\admin\auth-logo.svg                           (D:\daakia-web-app\src\utils\auth.util.js                           'D:\daakia-web-app\src\services\axios.js                           $D:\daakia-web-app\src\utils\axios.js                           =D:\daakia-web-app\public\assets\images\frontend\axis-bank.png                           BD:\daakia-web-app\src\styles\scss\admin\core\pages\auths\base.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\pages\errors\base.scss                           +D:\daakia-web-app\src\helpers\baseRoutes.js                           ED:\daakia-web-app\src\styles\scss\admin\bootstrap\bootstrap-grid.scss                           AD:\daakia-web-app\src\styles\scss\admin\libs\bootstrap-icons.scss                           GD:\daakia-web-app\src\styles\scss\admin\bootstrap\bootstrap-reboot.scss                           JD:\daakia-web-app\src\styles\scss\admin\bootstrap\bootstrap-utilities.scss                           @D:\daakia-web-app\src\styles\scss\admin\bootstrap\bootstrap.scss                           FD:\daakia-web-app\public\assets\images\frontend\how-works\box-1-bg.svg                           FD:\daakia-web-app\public\assets\images\frontend\how-works\box-2-bg.svg                           FD:\daakia-web-app\public\assets\images\frontend\how-works\box-3-bg.svg                           FD:\daakia-web-app\public\assets\images\frontend\how-works\box-4-bg.svg                           FD:\daakia-web-app\public\assets\images\frontend\how-works\box-5-bg.svg                           FD:\daakia-web-app\public\assets\images\frontend\how-works\box-6-bg.svg                           FD:\daakia-web-app\public\assets\images\frontend\how-works\box-7-bg.svg                           GD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\brands.scss                           ;D:\daakia-web-app\src\styles\scss\admin\vendors\bundle.scss                           CD:\daakia-web-app\public\assets\images\frontend\bundled-icon-bk.svg                           @D:\daakia-web-app\public\assets\images\frontend\bundled-icon.svg                           UD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\mixins\buttons.scss                           CD:\daakia-web-app\public\assets\images\frontend\characters-sign.svg                           ?D:\daakia-web-app\public\assets\images\frontend\chats-calls.svg                           CD:\daakia-web-app\public\assets\images\frontend\how-works\clips.svg                           :D:\daakia-web-app\src\styles\images\frontend\coin-icon.png                           *D:\daakia-web-app\src\utils\common.util.js                           ;D:\daakia-web-app\src\pages\User\LiveKit\utils\constants.js                           ;D:\daakia-web-app\public\assets\images\frontend\contact.svg                           AD:\daakia-web-app\src\pages\User\LiveKit\customFabs\ControlBar.js                           GD:\daakia-web-app\src\styles\scss\admin\components\ant-select\core.scss                           AD:\daakia-web-app\src\styles\scss\admin\vendors\select2\core.scss                           LD:\daakia-web-app\public\assets\images\frontend\how-works\create-account.svg                           =D:\daakia-web-app\src\styles\images\frontend\dashboard-bg.jpg                           8D:\daakia-web-app\src\styles\fonts\admin\DMSans-Bold.eot                           8D:\daakia-web-app\src\styles\fonts\admin\DMSans-Bold.ttf                           9D:\daakia-web-app\src\styles\fonts\admin\DMSans-Bold.woff                           :D:\daakia-web-app\src\styles\fonts\admin\DMSans-Bold.woff2                           :D:\daakia-web-app\src\styles\fonts\admin\DMSans-Medium.eot                           :D:\daakia-web-app\src\styles\fonts\admin\DMSans-Medium.ttf                           ;D:\daakia-web-app\src\styles\fonts\admin\DMSans-Medium.woff                           <D:\daakia-web-app\src\styles\fonts\admin\DMSans-Medium.woff2                           ;D:\daakia-web-app\src\styles\fonts\admin\DMSans-Regular.eot                           ;D:\daakia-web-app\src\styles\fonts\admin\DMSans-Regular.ttf                           <D:\daakia-web-app\src\styles\fonts\admin\DMSans-Regular.woff                           =D:\daakia-web-app\src\styles\fonts\admin\DMSans-Regular.woff2                           <D:\daakia-web-app\public\assets\images\frontend\doc-file.svg                           ?D:\daakia-web-app\public\assets\images\frontend\easy-to-use.svg                           D:\daakia-web-app\env.example                           DD:\daakia-web-app\public\assets\images\frontend\how-works\events.svg                           WD:\daakia-web-app\src\pages\User\ReferFriend\socialMediaShare.js\FacebookShareButton.js                           'D:\daakia-web-app\src\config\faqData.js                           $D:\daakia-web-app\public\favicon.ico                           8D:\daakia-web-app\public\assets\images\admin\favicon.svg                           AD:\daakia-web-app\src\styles\images\frontend\features-bg-line.svg                           <D:\daakia-web-app\src\styles\images\frontend\features-bg.svg                           @D:\daakia-web-app\public\assets\images\frontend\features-img.jpg                           AD:\daakia-web-app\public\assets\images\frontend\features-img.webp                           8D:\daakia-web-app\public\assets\images\frontend\flag.svg                           CD:\daakia-web-app\src\styles\scss\admin\libs\fontawesome-icons.scss                           LD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\fontawesome.scss                           ?D:\daakia-web-app\public\assets\images\frontend\footer-logo.png                           8D:\daakia-web-app\src\styles\scss\frontend\frontend.scss                           9D:\daakia-web-app\src\styles\images\frontend\green-bg.png                           =D:\daakia-web-app\public\assets\images\frontend\hdfc-bank.png                           BD:\daakia-web-app\public\assets\images\frontend\heading-border.png                           ?D:\daakia-web-app\src\styles\images\frontend\heading-border.png                           =D:\daakia-web-app\src\styles\images\frontend\home-banner1.jpg                           =D:\daakia-web-app\src\styles\images\frontend\home-banner2.jpg                           =D:\daakia-web-app\src\styles\images\frontend\home-banner3.jpg                           #D:\daakia-web-app\src\utils\i18n.js                           >D:\daakia-web-app\public\assets\images\frontend\icici-bank.png                           7D:\daakia-web-app\src\styles\fonts\frontend\icomoon.eot                           7D:\daakia-web-app\src\styles\fonts\frontend\icomoon.svg                           7D:\daakia-web-app\src\styles\fonts\frontend\icomoon.ttf                           8D:\daakia-web-app\src\styles\fonts\frontend\icomoon.woff                           HD:\daakia-web-app\src\components\UiElement\AudioRecorder\index-backup.js                           9D:\daakia-web-app\src\components\Antd\Alerts\index.ant.js                           =D:\daakia-web-app\src\components\Antd\Breadcrumb\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Button\index.ant.js                           7D:\daakia-web-app\src\components\Antd\Card\index.ant.js                           ;D:\daakia-web-app\src\components\Antd\Checkbox\index.ant.js                           ?D:\daakia-web-app\src\components\Antd\ConfirmPopup\index.ant.js                           :D:\daakia-web-app\src\components\Antd\Content\index.ant.js                           =D:\daakia-web-app\src\components\Antd\DatePicker\index.ant.js                           >D:\daakia-web-app\src\components\Antd\Description\index.ant.js                           ;D:\daakia-web-app\src\components\Antd\Dropdown\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Footer\index.ant.js                           7D:\daakia-web-app\src\components\Antd\Form\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Header\index.ant.js                           8D:\daakia-web-app\src\components\Antd\Input\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Layout\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Loader\index.ant.js                           7D:\daakia-web-app\src\components\Antd\Menu\index.ant.js                           ;D:\daakia-web-app\src\components\Antd\Password\index.ant.js                           :D:\daakia-web-app\src\components\Antd\Popover\index.ant.js                           8D:\daakia-web-app\src\components\Antd\Radio\index.ant.js                           7D:\daakia-web-app\src\components\Antd\Rate\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Search\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Select\index.ant.js                           8D:\daakia-web-app\src\components\Antd\Sider\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Switch\index.ant.js                           8D:\daakia-web-app\src\components\Antd\Table\index.ant.js                           ;D:\daakia-web-app\src\components\Antd\TextArea\index.ant.js                           9D:\daakia-web-app\src\components\Antd\Upload\index.ant.js                           ;D:\daakia-web-app\src\components\Antd\Tooltip\index.ant.jsx                           QD:\daakia-web-app\src\components\UiElement\Button\AcceptRejectButton\index.btn.js                           KD:\daakia-web-app\src\components\UiElement\Button\CancelButton\index.btn.js                           FD:\daakia-web-app\src\components\UiElement\Button\Common\index.btn.jsx                           ND:\daakia-web-app\src\components\UiElement\Button\GradientButton\index.btn.jsx                           D:\daakia-web-app\src\index.css                           OD:\daakia-web-app\src\components\Filter\Admin\ConsumptionFilter\index.filter.js                           \D:\daakia-web-app\src\components\Filter\Admin\ContactUs\PendingQueriesFilter\index.filter.js                           \D:\daakia-web-app\src\components\Filter\Admin\ContactUs\RepliedQueriesFilter\index.filter.js                           KD:\daakia-web-app\src\components\Filter\Admin\CouponsFilter\index.filter.js                           TD:\daakia-web-app\src\components\Filter\Admin\DiscountRequestsFilter\index.filter.js                           PD:\daakia-web-app\src\components\Filter\Admin\DocumentTypeFilter\index.filter.js                           YD:\daakia-web-app\src\components\Filter\Admin\ManageClientsPartnersFilter\index.filter.js                           UD:\daakia-web-app\src\components\Filter\Admin\ManageTeamMembersFilter\index.filter.js                           ND:\daakia-web-app\src\components\Filter\Admin\ManageUserFilter\index.filter.js                           MD:\daakia-web-app\src\components\Filter\Admin\ReferralsFilter\index.filter.js                           TD:\daakia-web-app\src\components\Filter\Admin\SubscriptionPlanFilter\index.filter.js                           VD:\daakia-web-app\src\components\Filter\Admin\TransactionHistoryFilter\index.filter.js                           YD:\daakia-web-app\src\components\Form\Admin\UserManagement\NotificationForm\index.form.js                           HD:\daakia-web-app\src\components\Form\User\AffirmativeForm\index.form.js                           GD:\daakia-web-app\src\components\Form\User\AssignUserForm\index.form.js                           GD:\daakia-web-app\src\components\Form\User\CoupanCodeForm\index.form.js                           GD:\daakia-web-app\src\components\Form\User\CreditCardForm\index.form.js                           LD:\daakia-web-app\src\components\Form\User\InternetBankingForm\index.form.js                           ND:\daakia-web-app\src\components\Form\User\PasswordProtectedForm\index.form.js                           DD:\daakia-web-app\src\components\Form\User\PaymentData\index.form.js                           DD:\daakia-web-app\src\components\Form\User\PaymentForm\index.form.js                           FD:\daakia-web-app\src\components\Form\User\RecordingForm\index.form.js                           BD:\daakia-web-app\src\components\Form\User\ShareForm\index.form.js                           JD:\daakia-web-app\src\components\Form\User\StartMeetingsForm\index.form.js                           @D:\daakia-web-app\src\components\Form\User\UPIForm\index.form.js                           DD:\daakia-web-app\src\components\Form\User\UserAccount\index.form.js                           FD:\daakia-web-app\src\components\Form\User\UserContactUs\index.form.js                           SD:\daakia-web-app\src\components\Form\Admin\AddEditClientPartnerForm\index.form.jsx                           JD:\daakia-web-app\src\components\Form\Admin\AddEditPlanForm\index.form.jsx                           LD:\daakia-web-app\src\components\Form\Admin\AddEditReasonForm\index.form.jsx                           PD:\daakia-web-app\src\components\Form\Admin\AddEditTeamMemberForm\index.form.jsx                           MD:\daakia-web-app\src\components\Form\Admin\ChangePasswordForm\index.form.jsx                           ID:\daakia-web-app\src\components\Form\Admin\DateFilterForm\index.form.jsx                           bD:\daakia-web-app\src\components\Form\Admin\DiscountRequestForms\DiscountAmountForm\index.form.jsx                           `D:\daakia-web-app\src\components\Form\Admin\DiscountRequestForms\ReasonRejectForm\index.form.jsx                           GD:\daakia-web-app\src\components\Form\Admin\EditFAQSForm\index.form.jsx                           ID:\daakia-web-app\src\components\Form\Admin\ForgotPassword\index.form.jsx                           JD:\daakia-web-app\src\components\Form\Admin\HeadquarterForm\index.form.jsx                           @D:\daakia-web-app\src\components\Form\Admin\Login\index.form.jsx                           RD:\daakia-web-app\src\components\Form\Admin\ManageCms\CookiesPolicy\index.form.jsx                           RD:\daakia-web-app\src\components\Form\Admin\ManageCms\PrivacyPolicy\index.form.jsx                           WD:\daakia-web-app\src\components\Form\Admin\ManageCms\TermsAndConditions\index.form.jsx                           JD:\daakia-web-app\src\components\Form\Admin\ManageTaxesForm\index.form.jsx                           ND:\daakia-web-app\src\components\Form\Admin\Master\DocumentType\index.form.jsx                           JD:\daakia-web-app\src\components\Form\Admin\OtpVerification\index.form.jsx                           HD:\daakia-web-app\src\components\Form\Admin\PromoCodeForm\index.form.jsx                           HD:\daakia-web-app\src\components\Form\Admin\ResetPassword\index.form.jsx                           MD:\daakia-web-app\src\components\Form\Admin\SendNewsletterForm\index.form.jsx                           HD:\daakia-web-app\src\components\Form\Admin\SendReplyForm\index.form.jsx                           HD:\daakia-web-app\src\components\Form\Admin\UpdateProfile\index.form.jsx                           JD:\daakia-web-app\src\components\Form\Admin\UpdateReferrals\index.form.jsx                           KD:\daakia-web-app\src\components\Form\Admin\UpdateSocialLink\index.form.jsx                           LD:\daakia-web-app\src\components\Form\Common\VerificationForm\index.form.jsx                           ID:\daakia-web-app\src\components\Form\User\PlanMeetingForm\index.form.jsx                           ND:\daakia-web-app\src\components\Form\User\SignUpForm\Step1Form\index.form.jsx                           ND:\daakia-web-app\src\components\Form\User\SignUpForm\Step2Form\index.form.jsx                           ND:\daakia-web-app\src\components\Form\User\SignUpForm\Step3Form\index.form.jsx                           GD:\daakia-web-app\src\components\Form\User\SubscribeForm\index.form.jsx                           RD:\daakia-web-app\src\components\Form\User\UserAccount\AddStateForm\index.form.jsx                           UD:\daakia-web-app\src\components\Form\User\UserAccount\UserAccountForm\index.form.jsx                           VD:\daakia-web-app\src\components\Form\User\UserAccount\UserBusinessForm\index.form.jsx                           eD:\daakia-web-app\src\components\Form\User\UserAccount\UserEmailPhoneForm\AddEmailForm\index.form.jsx                           hD:\daakia-web-app\src\components\Form\User\UserAccount\UserEmailPhoneForm\ChangeEmailForm\index.form.jsx                           iD:\daakia-web-app\src\components\Form\User\UserAccount\UserEmailPhoneForm\VerificationForm\index.form.jsx                           CD:\daakia-web-app\src\components\Form\User\UserLogin\index.form.jsx                           ND:\daakia-web-app\src\components\Form\User\UserVerificationForm\index.form.jsx                           #D:\daakia-web-app\public\index.html                           3D:\daakia-web-app\src\i18n\en\AboutUs\index.i18n.js                           9D:\daakia-web-app\src\i18n\en\Accessibility\index.i18n.js                           =D:\daakia-web-app\src\i18n\en\Admin\AdminHeader\index.i18n.js                           DD:\daakia-web-app\src\i18n\en\Admin\AdminUpdateProfile\index.i18n.js                           GD:\daakia-web-app\src\i18n\en\Admin\AdminUpdateSocialLink\index.i18n.js                           JD:\daakia-web-app\src\i18n\en\Admin\ContactUs\PendingQueries\index.i18n.js                           AD:\daakia-web-app\src\i18n\en\Admin\DiscountRequest\index.i18n.js                           ED:\daakia-web-app\src\i18n\en\Admin\ManageClientPartner\index.i18n.js                           ID:\daakia-web-app\src\i18n\en\Admin\ManageCms\CookiesPolicy\index.i18n.js                           @D:\daakia-web-app\src\i18n\en\Admin\ManageCms\FAQs\index.i18n.js                           ID:\daakia-web-app\src\i18n\en\Admin\ManageCms\ManageCmsList\index.i18n.js                           ID:\daakia-web-app\src\i18n\en\Admin\ManageCms\PrivacyPolicy\index.i18n.js                           MD:\daakia-web-app\src\i18n\en\Admin\ManageCms\TermsAndCondition\index.i18n.js                           =D:\daakia-web-app\src\i18n\en\Admin\ManageTaxes\index.i18n.js                           BD:\daakia-web-app\src\i18n\en\Admin\ManageTeamMember\index.i18n.js                           8D:\daakia-web-app\src\i18n\en\Admin\Master\index.i18n.js                           FD:\daakia-web-app\src\i18n\en\Admin\NewsletterSubscriber\index.i18n.js                           ;D:\daakia-web-app\src\i18n\en\Admin\Referrals\index.i18n.js                           DD:\daakia-web-app\src\i18n\en\Admin\TransactionHistory\index.i18n.js                           @D:\daakia-web-app\src\i18n\en\Admin\UserManagement\index.i18n.js                           :D:\daakia-web-app\src\i18n\en\ChangePassword\index.i18n.js                           2D:\daakia-web-app\src\i18n\en\Common\index.i18n.js                           5D:\daakia-web-app\src\i18n\en\Dashboard\index.i18n.js                           :D:\daakia-web-app\src\i18n\en\ForgotPassword\index.i18n.js                           7D:\daakia-web-app\src\i18n\en\LandingPage\index.i18n.js                           1D:\daakia-web-app\src\i18n\en\Login\index.i18n.js                           CD:\daakia-web-app\src\i18n\en\ManageCms\CookiesPolicy\index.i18n.js                           >D:\daakia-web-app\src\i18n\en\ManageSubscription\index.i18n.js                           2D:\daakia-web-app\src\i18n\en\Master\index.i18n.js                           8D:\daakia-web-app\src\i18n\en\Notification\index.i18n.js                           3D:\daakia-web-app\src\i18n\en\OurTeam\index.i18n.js                           4D:\daakia-web-app\src\i18n\en\Partners\index.i18n.js                           3D:\daakia-web-app\src\i18n\en\Profile\index.i18n.js                           5D:\daakia-web-app\src\i18n\en\PromoCode\index.i18n.js                           9D:\daakia-web-app\src\i18n\en\ResetPassword\index.i18n.js                           ;D:\daakia-web-app\src\i18n\en\User\CoupanCode\index.i18n.js                           7D:\daakia-web-app\src\i18n\en\User\Footer\index.i18n.js                           BD:\daakia-web-app\src\i18n\en\User\PasswordProtected\index.i18n.js                           >D:\daakia-web-app\src\i18n\en\User\PaymentMethod\index.i18n.js                           DD:\daakia-web-app\src\i18n\en\User\PlanAndSubscription\index.i18n.js                           <D:\daakia-web-app\src\i18n\en\User\ReferFriend\index.i18n.js                           @D:\daakia-web-app\src\i18n\en\User\ReferralRewards\index.i18n.js                           >D:\daakia-web-app\src\i18n\en\User\UserContactUs\index.i18n.js                           ;D:\daakia-web-app\src\i18n\en\User\UserHeader\index.i18n.js                           9D:\daakia-web-app\src\i18n\en\User\UserHome\index.i18n.js                           :D:\daakia-web-app\src\i18n\en\User\UserLogin\index.i18n.js                           <D:\daakia-web-app\src\i18n\en\User\UserProfile\index.i18n.js                           AD:\daakia-web-app\src\i18n\en\User\UserPurchasePlan\index.i18n.js                           ;D:\daakia-web-app\src\i18n\en\User\UserSignUP\index.i18n.js                           AD:\daakia-web-app\src\i18n\en\User\VdeoConferencing\index.i18n.js                           8D:\daakia-web-app\src\i18n\en\Verification\index.i18n.js                           6D:\daakia-web-app\src\apiEndPoints\Admin\Auth\index.js                           JD:\daakia-web-app\src\apiEndPoints\Admin\ContactUs\PendingQueries\index.js                           ;D:\daakia-web-app\src\apiEndPoints\Admin\Dashboard\index.js                           BD:\daakia-web-app\src\apiEndPoints\Admin\DiscountRequests\index.js                           1D:\daakia-web-app\src\apiEndPoints\Admin\index.js                           ED:\daakia-web-app\src\apiEndPoints\Admin\ManageClientPartner\index.js                           @D:\daakia-web-app\src\apiEndPoints\Admin\ManageCMS\FAQs\index.js                           DD:\daakia-web-app\src\apiEndPoints\Admin\ManageSubscription\index.js                           =D:\daakia-web-app\src\apiEndPoints\Admin\ManageTaxes\index.js                           BD:\daakia-web-app\src\apiEndPoints\Admin\ManageTeamMember\index.js                           HD:\daakia-web-app\src\apiEndPoints\Admin\Master\ContactUsReason\index.js                           ED:\daakia-web-app\src\apiEndPoints\Admin\Master\DocumentType\index.js                           CD:\daakia-web-app\src\apiEndPoints\Admin\Master\Headquater\index.js                           7D:\daakia-web-app\src\apiEndPoints\Admin\Media\index.js                           GD:\daakia-web-app\src\apiEndPoints\Admin\NewsletterSubscribers\index.js                           >D:\daakia-web-app\src\apiEndPoints\Admin\Notification\index.js                           SD:\daakia-web-app\src\apiEndPoints\Admin\PromotionsAndReferrals\Promotions\index.js                           RD:\daakia-web-app\src\apiEndPoints\Admin\PromotionsAndReferrals\Referrals\index.js                           DD:\daakia-web-app\src\apiEndPoints\Admin\TransactionHistory\index.js                           @D:\daakia-web-app\src\apiEndPoints\Admin\UserManagement\index.js                           2D:\daakia-web-app\src\apiEndPoints\Common\index.js                           +D:\daakia-web-app\src\apiEndPoints\index.js                           5D:\daakia-web-app\src\apiEndPoints\User\Auth\index.js                           ;D:\daakia-web-app\src\apiEndPoints\User\CoupanCode\index.js                           7D:\daakia-web-app\src\apiEndPoints\User\Footer\index.js                           0D:\daakia-web-app\src\apiEndPoints\User\index.js                           :D:\daakia-web-app\src\apiEndPoints\User\ManageCms\index.js                           6D:\daakia-web-app\src\apiEndPoints\User\Media\index.js                           8D:\daakia-web-app\src\apiEndPoints\User\Payment\index.js                           8D:\daakia-web-app\src\apiEndPoints\User\Profile\index.js                           :D:\daakia-web-app\src\apiEndPoints\User\Refferals\index.js                           :D:\daakia-web-app\src\apiEndPoints\User\Translate\index.js                           >D:\daakia-web-app\src\apiEndPoints\User\UserContactUs\index.js                           9D:\daakia-web-app\src\apiEndPoints\User\UserHome\index.js                           =D:\daakia-web-app\src\apiEndPoints\User\UserServices\index.js                           @D:\daakia-web-app\src\apiEndPoints\User\VideoConference\index.js                           5D:\daakia-web-app\src\components\AdminFooter\index.js                           5D:\daakia-web-app\src\components\AdminHeader\index.js                           .D:\daakia-web-app\src\components\Antd\index.js                           5D:\daakia-web-app\src\components\Antd\Search\index.js                           0D:\daakia-web-app\src\components\Common\index.js                           >D:\daakia-web-app\src\components\Common\ListingHeader\index.js                           ;D:\daakia-web-app\src\components\Common\PageHeader\index.js                           <D:\daakia-web-app\src\components\Common\PaymentMode\index.js                           6D:\daakia-web-app\src\components\Filter\Admin\index.js                           0D:\daakia-web-app\src\components\Filter\index.js                           3D:\daakia-web-app\src\components\Formatter\index.js                           4D:\daakia-web-app\src\components\Form\Admin\index.js                           >D:\daakia-web-app\src\components\Form\Admin\ManageCms\index.js                           ;D:\daakia-web-app\src\components\Form\Admin\Master\index.js                           CD:\daakia-web-app\src\components\Form\Admin\UserManagement\index.js                           5D:\daakia-web-app\src\components\Form\Common\index.js                           .D:\daakia-web-app\src\components\Form\index.js                           3D:\daakia-web-app\src\components\Form\User\index.js                           CD:\daakia-web-app\src\components\Form\User\SavedOptionForm\index.js                           )D:\daakia-web-app\src\components\index.js                           1D:\daakia-web-app\src\components\Sidebar\index.js                           =D:\daakia-web-app\src\components\UiElement\Accordion\index.js                           AD:\daakia-web-app\src\components\UiElement\AcountSidebar\index.js                           AD:\daakia-web-app\src\components\UiElement\AudioRecorder\index.js                           <D:\daakia-web-app\src\components\UiElement\AuthLogo\index.js                           >D:\daakia-web-app\src\components\UiElement\Breadcrumb\index.js                           GD:\daakia-web-app\src\components\UiElement\Button\ActionButton\index.js                           GD:\daakia-web-app\src\components\UiElement\Button\CreateButton\index.js                           DD:\daakia-web-app\src\components\UiElement\Button\CsvExport\index.js                           GD:\daakia-web-app\src\components\UiElement\Button\FilterButton\index.js                           :D:\daakia-web-app\src\components\UiElement\Button\index.js                           9D:\daakia-web-app\src\components\UiElement\Chart\index.js                           JD:\daakia-web-app\src\components\UiElement\DashboardCardComponent\index.js                           :D:\daakia-web-app\src\components\UiElement\Footer\index.js                           @D:\daakia-web-app\src\components\UiElement\GlobalLoader\index.js                           BD:\daakia-web-app\src\components\UiElement\HeaderDropdown\index.js                           :D:\daakia-web-app\src\components\UiElement\Header\index.js                           @D:\daakia-web-app\src\components\UiElement\ImageElement\index.js                           3D:\daakia-web-app\src\components\UiElement\index.js                           <D:\daakia-web-app\src\components\UiElement\MetaTags\index.js                           BD:\daakia-web-app\src\components\UiElement\MetaTags\Title\index.js                           9D:\daakia-web-app\src\components\UiElement\Modal\index.js                           AD:\daakia-web-app\src\components\UiElement\MultipleInput\index.js                           ?D:\daakia-web-app\src\components\UiElement\PageHeading\index.js                           >D:\daakia-web-app\src\components\UiElement\Pagination\index.js                           @D:\daakia-web-app\src\components\UiElement\RippleEffect\index.js                           >D:\daakia-web-app\src\components\UiElement\SweetAlert\index.js                           9D:\daakia-web-app\src\components\UiElement\Table\index.js                           8D:\daakia-web-app\src\components\UiElement\Tabs\index.js                           >D:\daakia-web-app\src\components\UiElement\TextEditor\index.js                           @D:\daakia-web-app\src\components\UiElement\VerticalTabs\index.js                           %D:\daakia-web-app\src\config\index.js                           &D:\daakia-web-app\src\helpers\index.js                           ,D:\daakia-web-app\src\i18n\en\Admin\index.js                           &D:\daakia-web-app\src\i18n\en\index.js                           +D:\daakia-web-app\src\i18n\en\User\index.js                           #D:\daakia-web-app\src\i18n\index.js                           D:\daakia-web-app\src\index.js                           &D:\daakia-web-app\src\layouts\index.js                           *D:\daakia-web-app\src\pages\Admin\index.js                           $D:\daakia-web-app\src\pages\index.js                           )D:\daakia-web-app\src\pages\User\index.js                           BD:\daakia-web-app\src\pages\User\PaymentMethod\CreditCard\index.js                           GD:\daakia-web-app\src\pages\User\PaymentMethod\InternetBanking\index.js                           ID:\daakia-web-app\src\pages\User\PaymentMethod\SavedPaymentOffer\index.js                           ;D:\daakia-web-app\src\pages\User\PaymentMethod\UPI\index.js                           ID:\daakia-web-app\src\pages\User\PlanSubscription\BillingHistory\index.js                           TD:\daakia-web-app\src\pages\User\PlanSubscription\CorporateSubscriptionPlan\index.js                           KD:\daakia-web-app\src\pages\User\PlanSubscription\SubscriptionPlan\index.js                           6D:\daakia-web-app\src\pages\User\VideoDisplay\index.js                           $D:\daakia-web-app\src\redux\index.js                           6D:\daakia-web-app\src\routeControl\Admin\Auth\index.js                           ;D:\daakia-web-app\src\routeControl\Admin\ContactUs\index.js                           ED:\daakia-web-app\src\routeControl\Admin\CouponsAndReferrals\index.js                           ;D:\daakia-web-app\src\routeControl\Admin\Dashboard\index.js                           BD:\daakia-web-app\src\routeControl\Admin\DiscountRequests\index.js                           1D:\daakia-web-app\src\routeControl\Admin\index.js                           GD:\daakia-web-app\src\routeControl\Admin\ManageClientsPartners\index.js                           ;D:\daakia-web-app\src\routeControl\Admin\ManageCms\index.js                           DD:\daakia-web-app\src\routeControl\Admin\ManageSubscription\index.js                           =D:\daakia-web-app\src\routeControl\Admin\ManageTaxes\index.js                           CD:\daakia-web-app\src\routeControl\Admin\ManageTeamMembers\index.js                           8D:\daakia-web-app\src\routeControl\Admin\Master\index.js                           GD:\daakia-web-app\src\routeControl\Admin\NewsletterSubscribers\index.js                           >D:\daakia-web-app\src\routeControl\Admin\Notification\index.js                           DD:\daakia-web-app\src\routeControl\Admin\TransactionHistory\index.js                           6D:\daakia-web-app\src\routeControl\Admin\User\index.js                           +D:\daakia-web-app\src\routeControl\index.js                           6D:\daakia-web-app\src\routeControl\PublicPage\index.js                           8D:\daakia-web-app\src\routeControl\User\AboutUs\index.js                           5D:\daakia-web-app\src\routeControl\User\Auth\index.js                           0D:\daakia-web-app\src\routeControl\User\index.js                           :D:\daakia-web-app\src\routeControl\User\ManageCms\index.js                           >D:\daakia-web-app\src\routeControl\User\Notifications\index.js                           8D:\daakia-web-app\src\routeControl\User\OurTeam\index.js                           9D:\daakia-web-app\src\routeControl\User\Partners\index.js                           >D:\daakia-web-app\src\routeControl\User\PaymentMethod\index.js                           ?D:\daakia-web-app\src\routeControl\User\PaymentSuccess\index.js                           ?D:\daakia-web-app\src\routeControl\User\PaymentSummary\index.js                           AD:\daakia-web-app\src\routeControl\User\PlanSubscription\index.js                           =D:\daakia-web-app\src\routeControl\User\PurchasePlan\index.js                           <D:\daakia-web-app\src\routeControl\User\ReferFriend\index.js                           @D:\daakia-web-app\src\routeControl\User\ReferralRewards\index.js                           <D:\daakia-web-app\src\routeControl\User\Translation\index.js                           >D:\daakia-web-app\src\routeControl\User\UserContactUs\index.js                           >D:\daakia-web-app\src\routeControl\User\UserDashboard\index.js                           <D:\daakia-web-app\src\routeControl\User\UserProfile\index.js                           BD:\daakia-web-app\src\routeControl\User\VideoConferencing\index.js                           =D:\daakia-web-app\src\routeControl\User\VideoDisplay\index.js                           +D:\daakia-web-app\src\routes\Admin\index.js                           %D:\daakia-web-app\src\routes\index.js                           *D:\daakia-web-app\src\routes\User\index.js                           -D:\daakia-web-app\src\services\Admin\index.js                           'D:\daakia-web-app\src\services\index.js                           ,D:\daakia-web-app\src\services\User\index.js                           $D:\daakia-web-app\src\utils\index.js                           <D:\daakia-web-app\src\apiEndPoints\Admin\ManageCMS\Index.jsx                           3D:\daakia-web-app\src\layouts\Admin\index.layout.js                           2D:\daakia-web-app\src\layouts\User\index.layout.js                           3D:\daakia-web-app\src\layouts\Auth\index.layout.jsx                           3D:\daakia-web-app\src\layouts\Main\index.layout.jsx                           @D:\daakia-web-app\src\components\fullPageLoader\index.loader.jsx                           DD:\daakia-web-app\src\pages\Admin\Account\AdminProfile\index.page.js                           FD:\daakia-web-app\src\pages\Admin\Account\ChangePassword\index.page.js                           HD:\daakia-web-app\src\pages\Admin\ContactUs\PendingQueries\index.page.js                           HD:\daakia-web-app\src\pages\Admin\ContactUs\RepliedQueries\index.page.js                           >D:\daakia-web-app\src\pages\Admin\CouponsHistory\index.page.js                           9D:\daakia-web-app\src\pages\Admin\Dashboard\index.page.js                           @D:\daakia-web-app\src\pages\Admin\DiscountRequests\index.page.js                           >D:\daakia-web-app\src\pages\Admin\ForgotPassword\index.page.js                           5D:\daakia-web-app\src\pages\Admin\Login\index.page.js                           ED:\daakia-web-app\src\pages\Admin\ManageClientsPartners\index.page.js                           GD:\daakia-web-app\src\pages\Admin\ManageCms\CookiesPolicy\index.page.js                           JD:\daakia-web-app\src\pages\Admin\ManageCms\EndUserAgreement\index.page.js                           >D:\daakia-web-app\src\pages\Admin\ManageCms\FAQs\index.page.js                           9D:\daakia-web-app\src\pages\Admin\ManageCms\index.page.js                           GD:\daakia-web-app\src\pages\Admin\ManageCms\PrivacyPolicy\index.page.js                           BD:\daakia-web-app\src\pages\Admin\ManageSubscription\index.page.js                           ;D:\daakia-web-app\src\pages\Admin\ManageTaxes\index.page.js                           AD:\daakia-web-app\src\pages\Admin\ManageTeamMembers\index.page.js                           FD:\daakia-web-app\src\pages\Admin\Master\ContactUsReason\index.page.js                           CD:\daakia-web-app\src\pages\Admin\Master\DocumentType\index.page.js                           BD:\daakia-web-app\src\pages\Admin\Master\Headquarter\index.page.js                           DD:\daakia-web-app\src\pages\Admin\Master\JitsiFeatures\index.page.js                           ED:\daakia-web-app\src\pages\Admin\NewsletterSubscribers\index.page.js                           ?D:\daakia-web-app\src\pages\Admin\NotificationAll\index.page.js                           ?D:\daakia-web-app\src\pages\Admin\OtpVerification\index.page.js                           QD:\daakia-web-app\src\pages\Admin\PromotionsAndReferrals\Promotions\index.page.js                           PD:\daakia-web-app\src\pages\Admin\PromotionsAndReferrals\Referrals\index.page.js                           =D:\daakia-web-app\src\pages\Admin\ResetPassword\index.page.js                           BD:\daakia-web-app\src\pages\Admin\TransactionHistory\index.page.js                           UD:\daakia-web-app\src\pages\Admin\Users\ManageCustomers\CustomerDetails\index.page.js                           UD:\daakia-web-app\src\pages\Admin\Users\ManageCustomers\CustomerListing\index.page.js                           2D:\daakia-web-app\src\pages\NotFound\index.page.js                           6D:\daakia-web-app\src\pages\User\AboutUs\index.page.js                           3D:\daakia-web-app\src\pages\User\Home\index.page.js                           6D:\daakia-web-app\src\pages\User\Invitee\index.page.js                           8D:\daakia-web-app\src\pages\User\JitsiMeet\index.page.js                           6D:\daakia-web-app\src\pages\User\LiveKit\index.page.js                           4D:\daakia-web-app\src\pages\User\Login\index.page.js                           FD:\daakia-web-app\src\pages\User\ManageCms\CookiesPolicy\index.page.js                           ID:\daakia-web-app\src\pages\User\ManageCms\EndUserAgreement\index.page.js                           =D:\daakia-web-app\src\pages\User\ManageCms\FAQs\index.page.js                           8D:\daakia-web-app\src\pages\User\ManageCms\index.page.js                           FD:\daakia-web-app\src\pages\User\ManageCms\PrivacyPolicy\index.page.js                           <D:\daakia-web-app\src\pages\User\Notifications\index.page.js                           6D:\daakia-web-app\src\pages\User\OurTeam\index.page.js                           7D:\daakia-web-app\src\pages\User\Partners\index.page.js                           <D:\daakia-web-app\src\pages\User\PaymentMethod\index.page.js                           =D:\daakia-web-app\src\pages\User\PaymentSuccess\index.page.js                           =D:\daakia-web-app\src\pages\User\PaymentSummary\index.page.js                           ?D:\daakia-web-app\src\pages\User\PlanSubscription\index.page.js                           ;D:\daakia-web-app\src\pages\User\PurchasePlan\index.page.js                           :D:\daakia-web-app\src\pages\User\ReferFriend\index.page.js                           >D:\daakia-web-app\src\pages\User\ReferralRewards\index.page.js                           5D:\daakia-web-app\src\pages\User\Signup\index.page.js                           :D:\daakia-web-app\src\pages\User\Translation\index.page.js                           @D:\daakia-web-app\src\pages\User\UnRegisterInvitee\index.page.js                           BD:\daakia-web-app\src\pages\User\UnRegisterJitsiMeet\index.page.js                           DD:\daakia-web-app\src\pages\User\UnregisterTranslation\index.page.js                           :D:\daakia-web-app\src\pages\User\UserAccount\index.page.js                           <D:\daakia-web-app\src\pages\User\UserContactUs\index.page.js                           <D:\daakia-web-app\src\pages\User\UserDashboard\index.page.js                           @D:\daakia-web-app\src\pages\User\VideoConferencing\index.page.js                           9D:\daakia-web-app\src\routes\Admin\Account\index.route.js                           ;D:\daakia-web-app\src\routes\Admin\ContactUs\index.route.js                           ED:\daakia-web-app\src\routes\Admin\CouponsAndReferrals\index.route.js                           ;D:\daakia-web-app\src\routes\Admin\Dashboard\index.route.js                           BD:\daakia-web-app\src\routes\Admin\DiscountRequests\index.route.js                           GD:\daakia-web-app\src\routes\Admin\ManageClientsPartners\index.route.js                           ;D:\daakia-web-app\src\routes\Admin\ManageCms\index.route.js                           DD:\daakia-web-app\src\routes\Admin\ManageSubscription\index.route.js                           =D:\daakia-web-app\src\routes\Admin\ManageTaxes\index.route.js                           CD:\daakia-web-app\src\routes\Admin\ManageTeamMembers\index.route.js                           8D:\daakia-web-app\src\routes\Admin\Master\index.route.js                           GD:\daakia-web-app\src\routes\Admin\NewsletterSubscribers\index.route.js                           >D:\daakia-web-app\src\routes\Admin\Notification\index.route.js                           DD:\daakia-web-app\src\routes\Admin\TransactionHistory\index.route.js                           6D:\daakia-web-app\src\routes\Admin\User\index.route.js                           :D:\daakia-web-app\src\components\UiElement\Tabs\index.scss                           BD:\daakia-web-app\src\components\UiElement\VerticalTabs\index.scss                           :D:\daakia-web-app\src\services\Admin\Auth\index.service.js                           ND:\daakia-web-app\src\services\Admin\ContactUs\PendingQueries\index.service.js                           ?D:\daakia-web-app\src\services\Admin\Dashboard\index.service.js                           FD:\daakia-web-app\src\services\Admin\DiscountRequests\index.service.js                           ID:\daakia-web-app\src\services\Admin\ManageClientPartner\index.service.js                           DD:\daakia-web-app\src\services\Admin\ManageCMS\FAQs\index.service.js                           ?D:\daakia-web-app\src\services\Admin\ManageCMS\Index.service.js                           HD:\daakia-web-app\src\services\Admin\ManageSubscription\index.service.js                           AD:\daakia-web-app\src\services\Admin\ManageTaxes\index.service.js                           FD:\daakia-web-app\src\services\Admin\ManageTeamMember\index.service.js                           LD:\daakia-web-app\src\services\Admin\Master\ContactUsReason\index.service.js                           ID:\daakia-web-app\src\services\Admin\Master\DocumentType\index.service.js                           GD:\daakia-web-app\src\services\Admin\Master\Headqauter\index.service.js                           KD:\daakia-web-app\src\services\Admin\NewsletterSubscribers\index.service.js                           BD:\daakia-web-app\src\services\Admin\Notification\index.service.js                           WD:\daakia-web-app\src\services\Admin\PromotionsAndReferrals\Promotions\index.service.js                           VD:\daakia-web-app\src\services\Admin\PromotionsAndReferrals\Referrals\index.service.js                           HD:\daakia-web-app\src\services\Admin\TransactionHistory\index.service.js                           DD:\daakia-web-app\src\services\Admin\UserManagement\index.service.js                           6D:\daakia-web-app\src\services\Common\index.service.js                           9D:\daakia-web-app\src\services\User\Auth\index.service.js                           ?D:\daakia-web-app\src\services\User\CoupanCode\index.service.js                           <D:\daakia-web-app\src\services\User\Payment\index.service.js                           <D:\daakia-web-app\src\services\User\Profile\index.service.js                           DD:\daakia-web-app\src\services\User\RefferalService\index.service.js                           >D:\daakia-web-app\src\services\User\Translate\index.service.js                           AD:\daakia-web-app\src\services\User\UserContacUs\index.service.js                           =D:\daakia-web-app\src\services\User\UserHome\index.service.js                           AD:\daakia-web-app\src\services\User\UserServices\index.service.js                           DD:\daakia-web-app\src\services\User\VideoConference\index.service.js                           CD:\daakia-web-app\src\services\User\FooterService\index.services.js                           ?D:\daakia-web-app\src\services\User\ManageCms\index.services.js                           4D:\daakia-web-app\src\redux\AuthSlice\index.slice.js                           4D:\daakia-web-app\src\redux\UserSlice\index.slice.js                           )D:\daakia-web-app\public\invite-image.png                           D:\daakia-web-app\Jenkinsfile                           8D:\daakia-web-app\src\styles\scss\admin\libs\jstree.scss                           >D:\daakia-web-app\public\assets\images\frontend\kotak-bank.png                           FD:\daakia-web-app\public\assets\images\frontend\how-works\language.svg                           @D:\daakia-web-app\src\pages\User\Translation\LanguageDropdown.js                           WD:\daakia-web-app\src\styles\scss\admin\components\ant-select\theme\default\layout.scss                           QD:\daakia-web-app\src\styles\scss\admin\vendors\select2\theme\default\layout.scss                           %D:\daakia-web-app\src\utils\logger.js                           9D:\daakia-web-app\public\assets\images\frontend\login.jpg                           :D:\daakia-web-app\public\assets\images\admin\logo-dark.svg                           =D:\daakia-web-app\public\assets\images\frontend\logo-dark.svg                           ;D:\daakia-web-app\public\assets\images\admin\logo-light.svg                           >D:\daakia-web-app\public\assets\images\frontend\logo-light.svg                           @D:\daakia-web-app\public\assets\images\admin\logo-small-dark.svg                           AD:\daakia-web-app\public\assets\images\admin\logo-small-light.svg                           8D:\daakia-web-app\public\assets\images\frontend\logo.svg                           D:\daakia-web-app\src\logo.svg                           $D:\daakia-web-app\public\logo192.png                           $D:\daakia-web-app\public\logo512.png                           &D:\daakia-web-app\public\manifest.json                           <D:\daakia-web-app\src\pages\User\LiveKit\utils\mergeProps.js                           <D:\daakia-web-app\src\styles\scss\admin\core\pages\misc.scss                           4D:\daakia-web-app\src\components\Antd\Modal\Modal.js                           <D:\daakia-web-app\public\assets\images\frontend\mp3-file.svg                           <D:\daakia-web-app\public\assets\images\frontend\mp4-file.svg                           4D:\daakia-web-app\src\styles\fonts\admin\Nioicon.eot                           DD:\daakia-web-app\src\styles\scss\admin\vendors\nioicon\nioicon.scss                           4D:\daakia-web-app\src\styles\fonts\admin\Nioicon.svg                           4D:\daakia-web-app\src\styles\fonts\admin\Nioicon.ttf                           5D:\daakia-web-app\src\styles\fonts\admin\Nioicon.woff                           ,D:\daakia-web-app\src\utils\notifications.js                           @D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Black.eot                           @D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Black.svg                           @D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Black.ttf                           AD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Black.woff                           BD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Black.woff2                           ?D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Bold.eot                           ?D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Bold.svg                           ?D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Bold.ttf                           @D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Bold.woff                           AD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Bold.woff2                           DD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-ExtraBold.eot                           DD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-ExtraBold.svg                           DD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-ExtraBold.ttf                           ED:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-ExtraBold.woff                           FD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-ExtraBold.woff2                           @D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Light.eot                           @D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Light.svg                           @D:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Light.ttf                           AD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Light.woff                           BD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Light.woff2                           BD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Regular.eot                           BD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Regular.svg                           BD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Regular.ttf                           CD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Regular.woff                           DD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-Regular.woff2                           CD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-SemiBold.eot                           CD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-SemiBold.svg                           CD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-SemiBold.ttf                           DD:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-SemiBold.woff                           ED:\daakia-web-app\src\styles\fonts\frontend\NunitoSans-SemiBold.woff2                           9D:\daakia-web-app\src\styles\images\frontend\offer-bg.jpg                           >D:\daakia-web-app\public\assets\images\frontend\offer-img1.png                           ?D:\daakia-web-app\public\assets\images\frontend\offer-img1.webp                           >D:\daakia-web-app\public\assets\images\frontend\offer-img2.png                           ?D:\daakia-web-app\public\assets\images\frontend\offer-img2.webp                           #D:\daakia-web-app\package-lock.json                           D:\daakia-web-app\package.json                           GD:\daakia-web-app\src\pages\User\LiveKit\customFabs\ParticipantsList.js                           MD:\daakia-web-app\src\pages\User\LiveKit\components\ParticipantsListToggle.js                           @D:\daakia-web-app\public\assets\images\frontend\partner-img1.jpg                           @D:\daakia-web-app\public\assets\images\frontend\partner-img2.jpg                           @D:\daakia-web-app\public\assets\images\frontend\partner-img3.jpg                           CD:\daakia-web-app\public\assets\images\frontend\payment-success.svg                           <D:\daakia-web-app\public\assets\images\frontend\pdf-file.svg                           ?D:\daakia-web-app\public\assets\images\frontend\plan-switch.svg                           >D:\daakia-web-app\public\assets\images\frontend\play-store.png                           <D:\daakia-web-app\public\assets\images\frontend\ppt-file.svg                           5D:\daakia-web-app\src\layouts\Admin\private.layout.js                           4D:\daakia-web-app\src\layouts\User\private.layout.js                           3D:\daakia-web-app\src\routes\Admin\private.route.js                           2D:\daakia-web-app\src\routes\User\private.route.js                           <D:\daakia-web-app\public\assets\images\admin\profile-img.jpg                           @D:\daakia-web-app\public\assets\images\frontend\profile-main.jpg                           ;D:\daakia-web-app\public\assets\images\frontend\profile.jpg                           *D:\daakia-web-app\.vs\ProjectSettings.json                           4D:\daakia-web-app\src\layouts\Admin\public.layout.js                           3D:\daakia-web-app\src\layouts\User\public.layout.js                           2D:\daakia-web-app\src\routes\Admin\public.route.js                           ,D:\daakia-web-app\src\routes\public.route.js                           1D:\daakia-web-app\src\routes\User\public.route.js                           :D:\daakia-web-app\src\styles\scss\admin\editors\quill.scss                           D:\daakia-web-app\README.md                           ;D:\daakia-web-app\src\styles\images\frontend\refer-left.svg                           <D:\daakia-web-app\src\styles\images\frontend\refer-right.svg                           AD:\daakia-web-app\public\assets\images\frontend\referrel-link.svg                           HD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\regular.scss                           (D:\daakia-web-app\src\reportWebVitals.js                           :D:\daakia-web-app\public\assets\images\frontend\reward.svg                           #D:\daakia-web-app\public\robots.txt                           ?D:\daakia-web-app\public\assets\images\frontend\safe-secure.svg                           <D:\daakia-web-app\public\assets\images\frontend\sbi-bank.png                           :D:\daakia-web-app\src\styles\fonts\frontend\selection.json                           ID:\daakia-web-app\src\pages\User\LiveKit\components\SettingsMenuToggle.js                           #D:\daakia-web-app\src\setupTests.js                           <D:\daakia-web-app\public\assets\images\frontend\signup-1.jpg                           :D:\daakia-web-app\public\assets\images\frontend\signup.jpg                           @D:\daakia-web-app\public\assets\images\frontend\social-media.svg                           FD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\solid.scss                           CD:\daakia-web-app\public\assets\images\frontend\how-works\speak.svg                           GD:\daakia-web-app\src\pages\User\LiveKit\components\StartMediaButton.js                           DD:\daakia-web-app\public\assets\images\frontend\how-works\status.svg                           $D:\daakia-web-app\src\redux\store.js                           8D:\daakia-web-app\src\components\Antd\Popover\style.scss                           7D:\daakia-web-app\src\components\Antd\Search\style.scss                           7D:\daakia-web-app\src\components\Antd\Select\style.scss                           6D:\daakia-web-app\src\components\Antd\Table\style.scss                           :D:\daakia-web-app\src\components\fullPageLoader\style.scss                           /D:\daakia-web-app\src\config\subscriptonData.js                           ID:\daakia-web-app\src\styles\scss\admin\components\summernote-extend.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\summernote-lite.scss                           7D:\daakia-web-app\src\styles\fonts\admin\summernote.eot                           ?D:\daakia-web-app\src\styles\scss\admin\editors\summernote.scss                           7D:\daakia-web-app\src\styles\fonts\admin\summernote.ttf                           8D:\daakia-web-app\src\styles\fonts\admin\summernote.woff                           9D:\daakia-web-app\src\styles\fonts\admin\summernote.woff2                           =D:\daakia-web-app\src\styles\images\frontend\team-bg-left.svg                           >D:\daakia-web-app\src\styles\images\frontend\team-bg-right.svg                           <D:\daakia-web-app\public\assets\images\frontend\team-img.jpg                           2D:\daakia-web-app\src\i18n\en\AboutUs\text.i18n.js                           8D:\daakia-web-app\src\i18n\en\Accessibility\text.i18n.js                           <D:\daakia-web-app\src\i18n\en\Admin\AdminHeader\text.i18n.js                           CD:\daakia-web-app\src\i18n\en\Admin\AdminUpdateProfile\text.i18n.js                           FD:\daakia-web-app\src\i18n\en\Admin\AdminUpdateSocialLink\text.i18n.js                           ID:\daakia-web-app\src\i18n\en\Admin\ContactUs\PendingQueries\text.i18n.js                           @D:\daakia-web-app\src\i18n\en\Admin\DiscountRequest\text.i18n.js                           DD:\daakia-web-app\src\i18n\en\Admin\ManageClientPartner\text.i18n.js                           HD:\daakia-web-app\src\i18n\en\Admin\ManageCms\CookiesPolicy\text.i18n.js                           ?D:\daakia-web-app\src\i18n\en\Admin\ManageCms\FAQs\text.i18n.js                           HD:\daakia-web-app\src\i18n\en\Admin\ManageCms\ManageCmsList\text.i18n.js                           HD:\daakia-web-app\src\i18n\en\Admin\ManageCms\PrivacyPolicy\text.i18n.js                           LD:\daakia-web-app\src\i18n\en\Admin\ManageCms\TermsAndCondition\text.i18n.js                           <D:\daakia-web-app\src\i18n\en\Admin\ManageTaxes\text.i18n.js                           AD:\daakia-web-app\src\i18n\en\Admin\ManageTeamMember\text.i18n.js                           7D:\daakia-web-app\src\i18n\en\Admin\Master\text.i18n.js                           ED:\daakia-web-app\src\i18n\en\Admin\NewsletterSubscriber\text.i18n.js                           :D:\daakia-web-app\src\i18n\en\Admin\Referrals\text.i18n.js                           CD:\daakia-web-app\src\i18n\en\Admin\TransactionHistory\text.i18n.js                           ?D:\daakia-web-app\src\i18n\en\Admin\UserManagement\text.i18n.js                           9D:\daakia-web-app\src\i18n\en\ChangePassword\text.i18n.js                           1D:\daakia-web-app\src\i18n\en\Common\text.i18n.js                           4D:\daakia-web-app\src\i18n\en\Dashboard\text.i18n.js                           9D:\daakia-web-app\src\i18n\en\ForgotPassword\text.i18n.js                           6D:\daakia-web-app\src\i18n\en\LandingPage\text.i18n.js                           0D:\daakia-web-app\src\i18n\en\Login\text.i18n.js                           BD:\daakia-web-app\src\i18n\en\ManageCms\CookiesPolicy\text.i18n.js                           =D:\daakia-web-app\src\i18n\en\ManageSubscription\text.i18n.js                           1D:\daakia-web-app\src\i18n\en\Master\text.i18n.js                           7D:\daakia-web-app\src\i18n\en\Notification\text.i18n.js                           2D:\daakia-web-app\src\i18n\en\OurTeam\text.i18n.js                           3D:\daakia-web-app\src\i18n\en\Partners\text.i18n.js                           2D:\daakia-web-app\src\i18n\en\Profile\text.i18n.js                           4D:\daakia-web-app\src\i18n\en\PromoCode\text.i18n.js                           8D:\daakia-web-app\src\i18n\en\ResetPassword\text.i18n.js                           :D:\daakia-web-app\src\i18n\en\User\CoupanCode\text.i18n.js                           6D:\daakia-web-app\src\i18n\en\User\Footer\text.i18n.js                           AD:\daakia-web-app\src\i18n\en\User\PasswordProtected\text.i18n.js                           =D:\daakia-web-app\src\i18n\en\User\PaymentMethod\text.i18n.js                           CD:\daakia-web-app\src\i18n\en\User\PlanAndSubscription\text.i18n.js                           ;D:\daakia-web-app\src\i18n\en\User\ReferFriend\text.i18n.js                           ?D:\daakia-web-app\src\i18n\en\User\ReferralRewards\text.i18n.js                           =D:\daakia-web-app\src\i18n\en\User\UserContactUs\text.i18n.js                           :D:\daakia-web-app\src\i18n\en\User\UserHeader\text.i18n.js                           8D:\daakia-web-app\src\i18n\en\User\UserHome\text.i18n.js                           9D:\daakia-web-app\src\i18n\en\User\UserLogin\text.i18n.js                           ;D:\daakia-web-app\src\i18n\en\User\UserProfile\text.i18n.js                           @D:\daakia-web-app\src\i18n\en\User\UserPurchasePlan\text.i18n.js                           :D:\daakia-web-app\src\i18n\en\User\UserSignUP\text.i18n.js                           @D:\daakia-web-app\src\i18n\en\User\VdeoConferencing\text.i18n.js                           7D:\daakia-web-app\src\i18n\en\Verification\text.i18n.js                           =D:\daakia-web-app\src\styles\scss\admin\skins\theme-blue.scss                           AD:\daakia-web-app\src\styles\scss\admin\skins\theme-bluelite.scss                           AD:\daakia-web-app\src\styles\scss\admin\skins\theme-egyptian.scss                           >D:\daakia-web-app\src\styles\scss\admin\skins\theme-green.scss                           <D:\daakia-web-app\src\styles\scss\admin\skins\theme-red.scss                           ?D:\daakia-web-app\src\styles\scss\admin\libs\themify-icons.scss                           ,D:\daakia-web-app\src\config\timezoneData.js                           <D:\daakia-web-app\src\styles\scss\admin\editors\tinymce.scss                           JD:\daakia-web-app\public\assets\images\frontend\how-works\transactions.svg                           DD:\daakia-web-app\public\assets\images\frontend\translation-icon.svg                           HD:\daakia-web-app\public\assets\images\frontend\translation-services.svg                           <D:\daakia-web-app\src\styles\images\frontend\translation.jpg                           ?D:\daakia-web-app\public\assets\images\frontend\translation.svg                           >D:\daakia-web-app\public\assets\images\frontend\union-bank.png                           @D:\daakia-web-app\src\pages\User\LiveKit\hooks\useMediaQuerry.js                           0D:\daakia-web-app\src\routeControl\userRoutes.js                           3D:\daakia-web-app\src\config\userSubscriptonData.js                           CD:\daakia-web-app\src\pages\User\LiveKit\hooks\useSettingsToggle.js                           ID:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\v4-shims.scss                           ID:\daakia-web-app\src\i18n\en\Admin\AdminUpdateProfile\validation.i18n.js                           LD:\daakia-web-app\src\i18n\en\Admin\AdminUpdateSocialLink\validation.i18n.js                           OD:\daakia-web-app\src\i18n\en\Admin\ContactUs\PendingQueries\validation.i18n.js                           FD:\daakia-web-app\src\i18n\en\Admin\DiscountRequest\validation.i18n.js                           JD:\daakia-web-app\src\i18n\en\Admin\ManageClientPartner\validation.i18n.js                           ND:\daakia-web-app\src\i18n\en\Admin\ManageCms\CookiesPolicy\validation.i18n.js                           ED:\daakia-web-app\src\i18n\en\Admin\ManageCms\FAQs\validation.i18n.js                           ND:\daakia-web-app\src\i18n\en\Admin\ManageCms\ManageCmsList\validation.i18n.js                           ND:\daakia-web-app\src\i18n\en\Admin\ManageCms\PrivacyPolicy\validation.i18n.js                           RD:\daakia-web-app\src\i18n\en\Admin\ManageCms\TermsAndCondition\validation.i18n.js                           GD:\daakia-web-app\src\i18n\en\Admin\ManageTeamMember\validation.i18n.js                           =D:\daakia-web-app\src\i18n\en\Admin\Master\validation.i18n.js                           KD:\daakia-web-app\src\i18n\en\Admin\NewsletterSubscriber\validation.i18n.js                           @D:\daakia-web-app\src\i18n\en\Admin\Referrals\validation.i18n.js                           ID:\daakia-web-app\src\i18n\en\Admin\TransactionHistory\validation.i18n.js                           ED:\daakia-web-app\src\i18n\en\Admin\UserManagement\validation.i18n.js                           ?D:\daakia-web-app\src\i18n\en\ChangePassword\validation.i18n.js                           7D:\daakia-web-app\src\i18n\en\Common\validation.i18n.js                           ?D:\daakia-web-app\src\i18n\en\ForgotPassword\validation.i18n.js                           <D:\daakia-web-app\src\i18n\en\LandingPage\validation.i18n.js                           6D:\daakia-web-app\src\i18n\en\Login\validation.i18n.js                           HD:\daakia-web-app\src\i18n\en\ManageCms\CookiesPolicy\validation.i18n.js                           CD:\daakia-web-app\src\i18n\en\ManageSubscription\validation.i18n.js                           7D:\daakia-web-app\src\i18n\en\Master\validation.i18n.js                           8D:\daakia-web-app\src\i18n\en\Profile\validation.i18n.js                           :D:\daakia-web-app\src\i18n\en\PromoCode\validation.i18n.js                           >D:\daakia-web-app\src\i18n\en\ResetPassword\validation.i18n.js                           CD:\daakia-web-app\src\i18n\en\User\PaymentMethod\validation.i18n.js                           ID:\daakia-web-app\src\i18n\en\User\PlanAndSubscription\validation.i18n.js                           CD:\daakia-web-app\src\i18n\en\User\UserContactUs\validation.i18n.js                           ?D:\daakia-web-app\src\i18n\en\User\UserLogin\validation.i18n.js                           AD:\daakia-web-app\src\i18n\en\User\UserProfile\validation.i18n.js                           @D:\daakia-web-app\src\i18n\en\User\UserSignUP\validation.i18n.js                           FD:\daakia-web-app\src\i18n\en\User\VdeoConferencing\validation.i18n.js                           =D:\daakia-web-app\src\i18n\en\Verification\validation.i18n.js                           RD:\daakia-web-app\src\components\Form\Admin\AddEditClientPartnerForm\validation.js                           ID:\daakia-web-app\src\components\Form\Admin\AddEditPlanForm\validation.js                           KD:\daakia-web-app\src\components\Form\Admin\AddEditReasonForm\validation.js                           OD:\daakia-web-app\src\components\Form\Admin\AddEditTeamMemberForm\validation.js                           LD:\daakia-web-app\src\components\Form\Admin\ChangePasswordForm\validation.js                           aD:\daakia-web-app\src\components\Form\Admin\DiscountRequestForms\DiscountAmountForm\validation.js                           _D:\daakia-web-app\src\components\Form\Admin\DiscountRequestForms\ReasonRejectForm\validation.js                           FD:\daakia-web-app\src\components\Form\Admin\EditFAQSForm\validation.js                           HD:\daakia-web-app\src\components\Form\Admin\ForgotPassword\validation.js                           ID:\daakia-web-app\src\components\Form\Admin\HeadquarterForm\validation.js                           ?D:\daakia-web-app\src\components\Form\Admin\Login\validation.js                           QD:\daakia-web-app\src\components\Form\Admin\ManageCms\CookiesPolicy\validation.js                           QD:\daakia-web-app\src\components\Form\Admin\ManageCms\PrivacyPolicy\validation.js                           VD:\daakia-web-app\src\components\Form\Admin\ManageCms\TermsAndConditions\validation.js                           MD:\daakia-web-app\src\components\Form\Admin\Master\DocumentType\validation.js                           GD:\daakia-web-app\src\components\Form\Admin\PromoCodeForm\validation.js                           GD:\daakia-web-app\src\components\Form\Admin\ResetPassword\validation.js                           LD:\daakia-web-app\src\components\Form\Admin\SendNewsletterForm\validation.js                           GD:\daakia-web-app\src\components\Form\Admin\SendReplyForm\validation.js                           GD:\daakia-web-app\src\components\Form\Admin\UpdateProfile\validation.js                           ID:\daakia-web-app\src\components\Form\Admin\UpdateReferrals\validation.js                           JD:\daakia-web-app\src\components\Form\Admin\UpdateSocialLink\validation.js                           YD:\daakia-web-app\src\components\Form\Admin\UserManagement\NotificationForm\validation.js                           HD:\daakia-web-app\src\components\Form\User\AffirmativeForm\validation.js                           GD:\daakia-web-app\src\components\Form\User\AssignUserForm\validation.js                           GD:\daakia-web-app\src\components\Form\User\CreditCardForm\validation.js                           LD:\daakia-web-app\src\components\Form\User\InternetBankingForm\validation.js                           ND:\daakia-web-app\src\components\Form\User\PasswordProtectedForm\validation.js                           DD:\daakia-web-app\src\components\Form\User\PaymentData\validation.js                           DD:\daakia-web-app\src\components\Form\User\PaymentForm\validation.js                           HD:\daakia-web-app\src\components\Form\User\PlanMeetingForm\validation.js                           HD:\daakia-web-app\src\components\Form\User\SavedOptionForm\validation.js                           MD:\daakia-web-app\src\components\Form\User\SignUpForm\Step1Form\validation.js                           MD:\daakia-web-app\src\components\Form\User\SignUpForm\Step3Form\validation.js                           JD:\daakia-web-app\src\components\Form\User\StartMeetingsForm\validation.js                           FD:\daakia-web-app\src\components\Form\User\SubscribeForm\validation.js                           @D:\daakia-web-app\src\components\Form\User\UPIForm\validation.js                           QD:\daakia-web-app\src\components\Form\User\UserAccount\AddStateForm\validation.js                           TD:\daakia-web-app\src\components\Form\User\UserAccount\UserAccountForm\validation.js                           UD:\daakia-web-app\src\components\Form\User\UserAccount\UserBusinessForm\validation.js                           dD:\daakia-web-app\src\components\Form\User\UserAccount\UserEmailPhoneForm\AddEmailForm\validation.js                           gD:\daakia-web-app\src\components\Form\User\UserAccount\UserEmailPhoneForm\ChangeEmailForm\validation.js                           FD:\daakia-web-app\src\components\Form\User\UserContactUs\validation.js                           BD:\daakia-web-app\src\components\Form\User\UserLogin\validation.js                           JD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\variables.scss                           KD:\daakia-web-app\public\assets\images\frontend\video-conferencing-icon.svg                           FD:\daakia-web-app\public\assets\images\frontend\video-conferencing.png                           FD:\daakia-web-app\public\assets\images\frontend\video-conferencing.svg                           FD:\daakia-web-app\src\pages\User\LiveKit\customFabs\VideoConference.js                           ;D:\daakia-web-app\public\assets\images\frontend\webdaak.svg                           D:\daakia-web-app\yarn.lock                           AD:\daakia-web-app\src\styles\scss\admin\bootstrap\_accordion.scss                           HD:\daakia-web-app\src\styles\scss\admin\core\components\_accordions.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_alert.scss                           =D:\daakia-web-app\src\styles\scss\admin\bootstrap\_alert.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_alert.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_alert.scss                           AD:\daakia-web-app\src\styles\scss\admin\global\wgs\_analytic.scss                           JD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_animated.scss                           QD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_animations.scss                           ND:\daakia-web-app\src\styles\scss\admin\components\ant-form\_ant-checkbox.scss                           BD:\daakia-web-app\src\styles\scss\admin\components\_ant-modal.scss                           KD:\daakia-web-app\src\styles\scss\frontend\components\modal\_ant-modal.scss                           KD:\daakia-web-app\src\styles\scss\admin\components\ant-form\_ant-radio.scss                           ED:\daakia-web-app\src\styles\scss\admin\bootstrap\utilities\_api.scss                           ;D:\daakia-web-app\src\styles\scss\admin\apps\_asterisk.scss                           9D:\daakia-web-app\src\styles\scss\admin\apps\_attach.scss                           @D:\daakia-web-app\src\styles\scss\frontend\pages\_auth.page.scss                           GD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_backdrop.scss                           RD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_background.scss                           =D:\daakia-web-app\src\styles\scss\admin\bootstrap\_badge.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_badge.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_badge.scss                           ?D:\daakia-web-app\src\styles\scss\admin\core\layouts\_base.scss                           @D:\daakia-web-app\src\styles\scss\admin\global\tables\_base.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_block.scss                           KD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_body.scss                           UD:\daakia-web-app\src\styles\scss\admin\vendors\datepicker\_bootstrap-datepicker.scss                           UD:\daakia-web-app\src\styles\scss\admin\vendors\bootstrap-icons\_bootstrap-icons.scss                           HD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\_bootstrap.scss                           LD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_border-radius.scss                           QD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_bordered-pulled.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_borders.scss                           ID:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_box-shadow.scss                           BD:\daakia-web-app\src\styles\scss\admin\bootstrap\_breadcrumb.scss                           TD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_breadcrumb.scss                           JD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_breakpoints.scss                           QD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_btn-group.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\_button-group.scss                           VD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_button-group.scss                           RD:\daakia-web-app\src\styles\scss\admin\vendors\datatable\_buttons.bootstrap4.scss                           FD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_buttons.scss                           ?D:\daakia-web-app\src\styles\scss\admin\bootstrap\_buttons.scss                           QD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_buttons.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_buttons.scss                           <D:\daakia-web-app\src\styles\scss\admin\bootstrap\_card.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\components\_card.scss                           ND:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_card.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_caret.scss                           @D:\daakia-web-app\src\styles\scss\admin\bootstrap\_carousel.scss                           8D:\daakia-web-app\src\styles\scss\admin\apps\_chats.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_clearfix.scss                           GD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_clearfix.scss                           =D:\daakia-web-app\src\styles\scss\admin\bootstrap\_close.scss                           ?D:\daakia-web-app\src\styles\scss\frontend\pages\_cms.page.scss                           KD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_color-scheme.scss                           MD:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_colored-links.scss                           ND:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_common.scss                           ND:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_common.scss                           GD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\_common.scss                           OD:\daakia-web-app\src\styles\scss\frontend\components\_component.antpicker.scss                           QD:\daakia-web-app\src\styles\scss\frontend\components\_component.backheading.scss                           ID:\daakia-web-app\src\styles\scss\admin\components\_component.common.scss                           JD:\daakia-web-app\src\styles\scss\frontend\components\_component.form.scss                           MD:\daakia-web-app\src\styles\scss\frontend\components\_component.icomoon.scss                           GD:\daakia-web-app\src\styles\scss\admin\components\_component.main.scss                           TD:\daakia-web-app\src\styles\scss\frontend\components\_component.sectionheading.scss                           HD:\daakia-web-app\src\styles\scss\admin\components\_component.table.scss                           KD:\daakia-web-app\src\styles\scss\frontend\components\_component.table.scss                           LD:\daakia-web-app\src\styles\scss\frontend\components\_component.toastr.scss                           RD:\daakia-web-app\src\styles\scss\frontend\components\_component.verticaltabs.scss                           MD:\daakia-web-app\src\styles\scss\frontend\components\_components.button.scss                           KD:\daakia-web-app\src\styles\scss\frontend\components\_components.main.scss                           =D:\daakia-web-app\src\styles\scss\admin\core\_components.scss                           OD:\daakia-web-app\src\styles\scss\frontend\components\_component_antselect.scss                           CD:\daakia-web-app\src\styles\scss\frontend\pages\_contact.page.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_container.scss                           BD:\daakia-web-app\src\styles\scss\admin\bootstrap\_containers.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\tinymce\ui\_content.inline.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\tinymce\ui\_content.mobile.scss                           HD:\daakia-web-app\src\styles\scss\admin\vendors\tinymce\ui\_content.scss                           ED:\daakia-web-app\src\styles\scss\admin\vendors\tinymce\_content.scss                           FD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_core.scss                           KD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_core.scss                           VD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_custom-forms.scss                           <D:\daakia-web-app\src\styles\scss\admin\core\_dark-skin.scss                           @D:\daakia-web-app\src\styles\scss\frontend\pages\_dashboard.scss                           @D:\daakia-web-app\src\styles\scss\admin\_dashlite_variables.scss                           GD:\daakia-web-app\src\styles\scss\admin\core\components\_data-list.scss                           ID:\daakia-web-app\src\styles\scss\admin\vendors\datatable\_datatable.scss                           BD:\daakia-web-app\src\styles\scss\admin\global\wgs\_db-invest.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_deprecate.scss                           ?D:\daakia-web-app\src\styles\scss\admin\core\fonts\_dmsans.scss                           =D:\daakia-web-app\src\styles\scss\admin\vendors\_dragula.scss                           @D:\daakia-web-app\src\styles\scss\admin\bootstrap\_dropdown.scss                           LD:\daakia-web-app\src\styles\scss\admin\components\ant-select\_dropdown.scss                           FD:\daakia-web-app\src\styles\scss\admin\core\components\_dropdown.scss                           RD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_dropdown.scss                           FD:\daakia-web-app\src\styles\scss\admin\vendors\select2\_dropdown.scss                           PD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_dropdown.scss                           GD:\daakia-web-app\src\styles\scss\admin\vendors\dropzone\_dropzone.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\dual-listbox\_dual-listbox.scss                           BD:\daakia-web-app\src\styles\scss\admin\global\wgs\_ecommerce.scss                           GD:\daakia-web-app\src\styles\scss\frontend\elements\_elements.html.scss                           ID:\daakia-web-app\src\styles\scss\admin\vendors\summernote\_elements.scss                           ED:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\_extend.scss                           ED:\daakia-web-app\src\styles\scss\admin\core\components\_feature.scss                           ?D:\daakia-web-app\src\styles\scss\admin\apps\_file-manager.scss                           MD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_fixed-width.scss                           LD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_flex.scss                           MD:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_floating-labels.scss                           ED:\daakia-web-app\src\styles\scss\admin\vendors\summernote\_font.scss                           AD:\daakia-web-app\src\styles\scss\admin\core\layouts\_footer.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_form-check.scss                           JD:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_form-control.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_form-range.scss                           ID:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_form-select.scss                           GD:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_form-text.scss                           LD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_form.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_forms.scss                           =D:\daakia-web-app\src\styles\scss\admin\bootstrap\_forms.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_forms.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_forms.scss                           BD:\daakia-web-app\src\styles\scss\admin\vendors\_fullcalendar.scss                           AD:\daakia-web-app\src\styles\scss\admin\bootstrap\_functions.scss                           @D:\daakia-web-app\src\styles\scss\admin\global\wgs\_gallery.scss                           GD:\daakia-web-app\src\styles\scss\frontend\generic\_generic.scroll.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_gradients.scss                           ND:\daakia-web-app\src\styles\scss\admin\vendors\select2\mixins\_gradients.scss                           CD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_grid.scss                           <D:\daakia-web-app\src\styles\scss\admin\bootstrap\_grid.scss                           ND:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_grid.scss                           AD:\daakia-web-app\src\styles\scss\admin\core\layouts\_header.scss                           ?D:\daakia-web-app\src\styles\scss\admin\bootstrap\_helpers.scss                           @D:\daakia-web-app\src\styles\scss\frontend\pages\_home.page.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_icons.scss                           GD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_icons.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_image.scss                           >D:\daakia-web-app\src\styles\scss\admin\bootstrap\_images.scss                           8D:\daakia-web-app\src\styles\scss\admin\apps\_inbox.scss                           ID:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_input-group.scss                           @D:\daakia-web-app\src\styles\scss\admin\global\wgs\_invoice.scss                           =D:\daakia-web-app\src\styles\scss\admin\vendors\_jkanban.scss                           RD:\daakia-web-app\src\styles\scss\admin\vendors\timepicker\_jquery.timepicker.scss                           <D:\daakia-web-app\src\styles\scss\admin\vendors\_jqvmap.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_labels.scss                           HD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_larger.scss                           PD:\daakia-web-app\src\styles\scss\frontend\components\layout\_layout.footer.scss                           PD:\daakia-web-app\src\styles\scss\frontend\components\layout\_layout.header.scss                           :D:\daakia-web-app\src\styles\scss\admin\core\_layouts.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\components\_link.scss                           ND:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_link.scss                           ID:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_list-group.scss                           BD:\daakia-web-app\src\styles\scss\admin\bootstrap\_list-group.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\components\_list.scss                           FD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_list.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_lists.scss                           ID:\daakia-web-app\src\styles\scss\admin\vendors\magnific-popup\_main.scss                           9D:\daakia-web-app\src\styles\scss\admin\vendors\_map.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_media.scss                           GD:\daakia-web-app\src\styles\scss\frontend\pages\_meeting-password.scss                           DD:\daakia-web-app\src\styles\scss\admin\core\layouts\_menu-main.scss                           ;D:\daakia-web-app\src\styles\scss\admin\apps\_messages.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\components\_misc.scss                           7D:\daakia-web-app\src\styles\scss\admin\core\_misc.scss                           =D:\daakia-web-app\src\styles\scss\admin\global\wgs\_misc.scss                           >D:\daakia-web-app\src\styles\scss\admin\bootstrap\_mixins.scss                           HD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_mixins.scss                           ND:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_mixins.scss                           MD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_mixins.scss                           ND:\daakia-web-app\src\styles\scss\frontend\components\modal\_modal-common.scss                           ND:\daakia-web-app\src\styles\scss\frontend\components\modal\_modal-coupon.scss                           LD:\daakia-web-app\src\styles\scss\frontend\components\modal\_modal-main.scss                           TD:\daakia-web-app\src\styles\scss\frontend\components\modal\_modal-plan-meeting.scss                           UD:\daakia-web-app\src\styles\scss\frontend\components\modal\_modal-share-meeting.scss                           OD:\daakia-web-app\src\styles\scss\frontend\components\modal\_modal-success.scss                           =D:\daakia-web-app\src\styles\scss\admin\bootstrap\_modal.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_modal.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_modal.scss                           MD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_modal.scss                           @D:\daakia-web-app\src\styles\scss\admin\core\_mode-override.scss                           ZD:\daakia-web-app\src\styles\scss\admin\components\ant-select\theme\default\_multiple.scss                           LD:\daakia-web-app\src\styles\scss\admin\components\ant-select\_multiple.scss                           TD:\daakia-web-app\src\styles\scss\admin\vendors\select2\theme\default\_multiple.scss                           FD:\daakia-web-app\src\styles\scss\admin\vendors\select2\_multiple.scss                           ;D:\daakia-web-app\src\styles\scss\admin\bootstrap\_nav.scss                           AD:\daakia-web-app\src\styles\scss\admin\core\components\_nav.scss                           MD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_nav.scss                           >D:\daakia-web-app\src\styles\scss\admin\bootstrap\_navbar.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\utilites\_noclass.scss                           HD:\daakia-web-app\src\styles\scss\frontend\pages\_notification.page.scss                           KD:\daakia-web-app\src\styles\scss\admin\vendors\nouislider\_nouislider.scss                           ED:\daakia-web-app\src\styles\scss\frontend\objects\_objects.html.scss                           AD:\daakia-web-app\src\styles\scss\admin\bootstrap\_offcanvas.scss                           FD:\daakia-web-app\src\styles\scss\frontend\pages\_ourpartner.page.scss                           CD:\daakia-web-app\src\styles\scss\frontend\pages\_ourteam.page.scss                           AD:\daakia-web-app\src\styles\scss\frontend\pages\_pages.main.scss                           8D:\daakia-web-app\src\styles\scss\admin\core\_pages.scss                           ID:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_pagination.scss                           BD:\daakia-web-app\src\styles\scss\admin\bootstrap\_pagination.scss                           TD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_pagination.scss                           ED:\daakia-web-app\src\styles\scss\frontend\pages\_payment-method.scss                           GD:\daakia-web-app\src\styles\scss\frontend\components\_payment.box.scss                           DD:\daakia-web-app\src\styles\scss\admin\bootstrap\_placeholders.scss                           GD:\daakia-web-app\src\styles\scss\frontend\pages\_planSubscription.scss                           PD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_polyfills.scss                           ?D:\daakia-web-app\src\styles\scss\admin\bootstrap\_popover.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_popover.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_position.scss                           PD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_position.scss                           >D:\daakia-web-app\src\styles\scss\admin\vendors\_prettify.scss                           :D:\daakia-web-app\src\styles\scss\admin\core\_preview.scss                           @D:\daakia-web-app\src\styles\scss\admin\global\wgs\_pricing.scss                           AD:\daakia-web-app\src\styles\scss\admin\global\wgs\_products.scss                           CD:\daakia-web-app\src\styles\scss\frontend\pages\_profile.page.scss                           CD:\daakia-web-app\src\styles\scss\admin\global\comps\_profiles.scss                           @D:\daakia-web-app\src\styles\scss\admin\bootstrap\_progress.scss                           RD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_progress.scss                           FD:\daakia-web-app\src\styles\scss\admin\core\components\_projects.scss                           DD:\daakia-web-app\src\styles\scss\frontend\pages\_purchase-plan.scss                           AD:\daakia-web-app\src\styles\scss\admin\vendors\quill\_quill.scss                           DD:\daakia-web-app\src\styles\scss\admin\core\components\_rating.scss                           ED:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_ratio.scss                           >D:\daakia-web-app\src\styles\scss\admin\bootstrap\_reboot.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\utilites\_reboot.scss                           ND:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_reboot.scss                           HD:\daakia-web-app\src\styles\scss\frontend\pages\_refer-friend.page.scss                           @D:\daakia-web-app\src\styles\scss\admin\apps\_reply-comment.scss                           ID:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_reset-text.scss                           ED:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_resize.scss                           BD:\daakia-web-app\src\styles\scss\admin\bootstrap\vendor\_rfs.scss                           <D:\daakia-web-app\src\styles\scss\admin\bootstrap\_root.scss                           QD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_rotated-flipped.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_screen-reader.scss                           DD:\daakia-web-app\src\styles\scss\admin\core\components\_search.scss                           ND:\daakia-web-app\src\styles\scss\frontend\settings\_settings.breakpoints.scss                           GD:\daakia-web-app\src\styles\scss\frontend\settings\_settings.main.scss                           OD:\daakia-web-app\src\styles\scss\frontend\settings\_settings.placeholders.scss                           MD:\daakia-web-app\src\styles\scss\admin\vendors\magnific-popup\_settings.scss                           MD:\daakia-web-app\src\styles\scss\frontend\settings\_settings.typography.scss                           LD:\daakia-web-app\src\styles\scss\frontend\settings\_settings.variables.scss                           GD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_shims.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\layouts\_sidebar.scss                           ?D:\daakia-web-app\src\styles\scss\admin\vendors\_simplebar.scss                           XD:\daakia-web-app\src\styles\scss\admin\components\ant-select\theme\default\_single.scss                           RD:\daakia-web-app\src\styles\scss\admin\vendors\select2\theme\default\_single.scss                           DD:\daakia-web-app\src\styles\scss\admin\vendors\select2\_single.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\utilites\_sizing.scss                           ND:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_sizing.scss                           LD:\daakia-web-app\src\styles\scss\admin\vendors\tinymce\ui\_skin.mobile.scss                           ED:\daakia-web-app\src\styles\scss\admin\vendors\tinymce\ui\_skin.scss                           AD:\daakia-web-app\src\styles\scss\admin\vendors\slick\_slick.scss                           ED:\daakia-web-app\src\styles\scss\admin\core\components\_sliders.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\utilites\_spacing.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_spacing.scss                           @D:\daakia-web-app\src\styles\scss\admin\bootstrap\_spinners.scss                           @D:\daakia-web-app\src\styles\scss\admin\core\layouts\_split.scss                           ID:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_stacked.scss                           FD:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_stacks.scss                           ED:\daakia-web-app\src\styles\scss\admin\core\components\_stepper.scss                           ND:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_stretched-link.scss                           8D:\daakia-web-app\src\styles\scss\admin\core\_style.scss                           :D:\daakia-web-app\src\styles\scss\admin\global\_style.scss                           BD:\daakia-web-app\src\styles\scss\admin\vendors\jsTree\_style.scss                           MD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\_sweetalert2.scss                           MD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_table-variants.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_table.scss                           >D:\daakia-web-app\src\styles\scss\admin\bootstrap\_tables.scss                           PD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_tables.scss                           <D:\daakia-web-app\src\styles\scss\admin\vendors\_tagify.scss                           ED:\daakia-web-app\src\styles\scss\admin\global\comps\_tb-compact.scss                           AD:\daakia-web-app\src\styles\scss\admin\global\comps\_tb-odr.scss                           AD:\daakia-web-app\src\styles\scss\admin\global\comps\_tb-tnx.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\components\_team.scss                           LD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_text-truncate.scss                           OD:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_text-truncation.scss                           BD:\daakia-web-app\src\styles\scss\admin\core\components\_text.scss                           LD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_text.scss                           QD:\daakia-web-app\src\styles\scss\admin\vendors\themify-icons\_themify-icons.scss                           ND:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_theming.scss                           AD:\daakia-web-app\src\styles\scss\admin\global\wgs\_timeline.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_title.scss                           OD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\components\_toast.scss                           CD:\daakia-web-app\src\styles\scss\admin\vendors\toastr\_toastr.scss                           XD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_toasts-animations.scss                           RD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_toasts-body.scss                           >D:\daakia-web-app\src\styles\scss\admin\bootstrap\_toasts.scss                           MD:\daakia-web-app\src\styles\scss\admin\vendors\sweetalert2\scss\_toasts.scss                           DD:\daakia-web-app\src\styles\scss\admin\core\components\_toggle.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_toolbar.scss                           BD:\daakia-web-app\src\styles\scss\frontend\tools\_tools.mixin.scss                           ?D:\daakia-web-app\src\styles\scss\admin\bootstrap\_tooltip.scss                           OD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_tooltip.scss                           ID:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_transition.scss                           CD:\daakia-web-app\src\styles\scss\admin\bootstrap\_transitions.scss                           GD:\daakia-web-app\src\styles\scss\frontend\pages\_translation.page.scss                           <D:\daakia-web-app\src\styles\scss\admin\bootstrap\_type.scss                           LD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\utilites\_type.scss                           CD:\daakia-web-app\src\styles\scss\admin\core\components\_users.scss                           ;D:\daakia-web-app\src\styles\scss\admin\core\_utilites.scss                           KD:\daakia-web-app\src\styles\scss\frontend\utilities\_utilities.helper.scss                           ND:\daakia-web-app\src\styles\scss\frontend\utilities\_utilities.keyframes.scss                           ID:\daakia-web-app\src\styles\scss\frontend\utilities\_utilities.main.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_utilities.scss                           AD:\daakia-web-app\src\styles\scss\admin\bootstrap\_utilities.scss                           HD:\daakia-web-app\src\styles\scss\admin\bootstrap\forms\_validation.scss                           AD:\daakia-web-app\src\styles\scss\admin\bootstrap\_variables.scss                           HD:\daakia-web-app\src\styles\scss\admin\extend\bootstrap\_variables.scss                           KD:\daakia-web-app\src\styles\scss\admin\vendors\fontawesome\_variables.scss                           GD:\daakia-web-app\src\styles\scss\admin\vendors\nioicon\_variables.scss                           QD:\daakia-web-app\src\styles\scss\admin\vendors\summernote\styles\_variables.scss                           AD:\daakia-web-app\src\styles\scss\admin\skins\_variable_blue.scss                           ED:\daakia-web-app\src\styles\scss\admin\skins\_variable_bluelite.scss                           ED:\daakia-web-app\src\styles\scss\admin\skins\_variable_egyptian.scss                           BD:\daakia-web-app\src\styles\scss\admin\skins\_variable_green.scss                           @D:\daakia-web-app\src\styles\scss\admin\skins\_variable_red.scss                           ID:\daakia-web-app\src\styles\scss\frontend\pages\_video-conferencing.scss                           OD:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_visually-hidden.scss                           ND:\daakia-web-app\src\styles\scss\admin\bootstrap\mixins\_visually-hidden.scss                           BD:\daakia-web-app\src\styles\scss\admin\bootstrap\helpers\_vr.scss                           AD:\daakia-web-app\src\styles\scss\admin\core\components\_wgm.scss                           AD:\daakia-web-app\src\styles\scss\admin\global\comps\_wizard.scss                           